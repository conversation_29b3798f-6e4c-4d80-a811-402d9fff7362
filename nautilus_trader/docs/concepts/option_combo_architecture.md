# Option Combo Architecture in Nautilus Trader

## Overview

This document provides a comprehensive overview of the option combo system in nautilus_trader, covering instrument modeling, fill simulation, quote generation, execution engines, and data cataloging. The system enables sophisticated multi-leg option strategy trading with realistic backtesting and live trading capabilities.

## Core Architecture Components

### System Overview

```mermaid
graph TB
    subgraph "Instrument Layer"
        A[OptionContract] --> B[ComboLeg]
        B --> C[OptionCombo]
    end
    
    subgraph "Data Layer"
        D[Individual Leg Quotes] --> E[ComboMarketDataProvider]
        E --> F[Synthetic Combo Quotes]
        G[Live Broker Quotes] --> H[Native Combo Quotes]
    end
    
    subgraph "Execution Layer"
        I[ComboOrderBuilder] --> J[ComboMatchingEngine]
        K[Enhanced Fill Model] --> J
        J --> L[Component Fills]
    end
    
    subgraph "Storage Layer"
        F --> M[Data Catalog]
        H --> M
        L --> N[Execution History]
    end
    
    C --> E
    C --> I
    E --> J
```

## Instrument Modeling

### ComboLeg Structure

```python
class ComboLeg:
    """Represents a single leg within an option combo."""
    
    def __init__(self, instrument_id: InstrumentId, ratio: int):
        self.instrument_id = instrument_id  # e.g., "SPY240315C00500000.OPRA"
        self.ratio = ratio                  # +1 (buy), -1 (sell), +2, -2, etc.
    
    @property
    def side(self) -> OrderSide:
        return OrderSide.BUY if self.ratio > 0 else OrderSide.SELL
    
    @property
    def abs_ratio(self) -> int:
        return abs(self.ratio)
```

### OptionCombo Instrument

```python
class OptionCombo(Instrument):
    """Multi-leg option combination instrument (2-4 legs)."""
    
    def __init__(self, legs: list[ComboLeg], instrument_cache: dict, ...):
        # Auto-generate instrument ID from sorted legs
        self.id = generate_combo_instrument_id(legs, instrument_cache)
        self.legs = sort_combo_legs(legs, instrument_cache)
        self.underlying = underlying
        self.strategy_type = strategy_type  # "IRON_CONDOR", "STRADDLE", etc.
```

### Instrument ID Generation Algorithm

The system automatically generates deterministic instrument IDs:

```mermaid
flowchart TD
    A[Input: Combo Legs] --> B[Sort Legs]
    B --> C{Put before Call}
    C --> D{Descending Expiry}
    D --> E{Ascending Strike}
    E --> F[Format Each Leg]
    F --> G[Join with '/']
    G --> H[Add Venue]
    H --> I[Final ID]
    
    subgraph "Formatting Rules"
        J["Positive Ratio: '1'"]
        K["Negative Ratio: '(1)'"]
        L["Format: {ratio}{instrument.symbol}"]
    end
    
    F --> J
    F --> K
    F --> L
```

**Examples:**
- **Straddle**: `1SPY240315P00500000/1SPY240315C00500000.OPRA`
- **Iron Condor**: `1SPY240315P00480000/(1)SPY240315P00490000/(1)SPY240315C00510000/1SPY240315C00520000.OPRA`

## Quote Generation System

### Backtesting: Synthetic Quote Generation

```mermaid
sequenceDiagram
    participant LegQuotes as Individual Leg Quotes
    participant Provider as ComboMarketDataProvider
    participant Cache as Quote Cache
    participant Engine as Backtest Engine
    participant Catalog as Data Catalog

    LegQuotes->>Provider: QuoteTick (Leg A)
    Provider->>Cache: Store Leg Quote
    LegQuotes->>Provider: QuoteTick (Leg B)
    Provider->>Cache: Store Leg Quote
    Provider->>Provider: Check All Legs Available
    Provider->>Provider: Calculate Combo Price
    Provider->>Provider: Generate Synthetic Quote
    Provider->>Engine: Publish Combo Quote
    Provider->>Catalog: Store Quote (Optional)
```

#### Synthetic Quote Calculation

```python
def _generate_combo_quote(self, combo: OptionCombo) -> QuoteTick:
    """Generate synthetic combo quote from leg quotes."""
    
    # Calculate combo bid/ask from leg quotes
    combo_bid_price = 0.0
    combo_ask_price = 0.0
    
    for leg in combo.legs:
        leg_quote = self._leg_quotes.get(leg.instrument_id)
        if leg.ratio > 0:  # Buy leg
            combo_bid_price += leg.ratio * leg_quote.bid_price
            combo_ask_price += leg.ratio * leg_quote.ask_price
        else:  # Sell leg
            combo_bid_price += leg.ratio * leg_quote.ask_price  # Sell at ask
            combo_ask_price += leg.ratio * leg_quote.bid_price  # Buy at bid
    
    # Apply vega-weighted spread adjustments
    if combo.vega_multiplier > 0:
        vega_adjustment = self._calculate_vega_spread(combo)
        combo_bid_price -= vega_adjustment
        combo_ask_price += vega_adjustment
    
    return QuoteTick(
        instrument_id=combo.id,
        bid_price=Price.from_str(f"{combo_bid_price:.{combo.price_precision}f}"),
        ask_price=Price.from_str(f"{combo_ask_price:.{combo.price_precision}f}"),
        bid_size=min_bid_size,
        ask_size=min_ask_size,
        ts_event=max_ts_event,
        ts_init=self._clock.timestamp_ns(),
    )
```

### Live Trading: Native Broker Quotes

```mermaid
graph LR
    subgraph "Interactive Brokers"
        A[BAG Contract] --> B[Native Combo Quotes]
    end
    
    subgraph "Nautilus Trader"
        B --> C[IB Data Client]
        C --> D[Standard Tick Subscription]
        D --> E[QuoteTick Objects]
        E --> F[Cache & Distribution]
    end
    
    subgraph "Downstream"
        F --> G[Execution Engines]
        F --> H[Data Catalog]
        F --> I[Strategy Components]
    end
```

## Enhanced Fill Model System

### Fill Probability Calculation

The enhanced fill model considers multiple market factors:

```python
class EnhancedComboFillModel:
    """Sophisticated fill model for option combo orders."""
    
    def calculate_fill_probability(
        self,
        combo: OptionCombo,
        order_side: OrderSide,
        order_price: Price,
        market_quote: QuoteTick,
        market_conditions: dict
    ) -> float:
        """Calculate probability of combo order execution."""
        
        # Base probability from price competitiveness
        base_prob = self._calculate_base_probability(order_price, market_quote, order_side)
        
        # Volatility adjustment
        vol_adjustment = self._calculate_volatility_adjustment(
            combo, market_conditions.get('implied_volatility', 0.2)
        )
        
        # Time-of-day liquidity adjustment
        time_adjustment = self._calculate_time_adjustment(
            market_conditions.get('current_time')
        )
        
        # Market maker vs retail execution modeling
        execution_profile = market_conditions.get('execution_profile', 'retail')
        profile_adjustment = self._calculate_profile_adjustment(execution_profile)
        
        # Combine all factors
        final_probability = base_prob * vol_adjustment * time_adjustment * profile_adjustment
        
        return min(1.0, max(0.0, final_probability))
```

### Market Microstructure Integration

```mermaid
graph TB
    subgraph "Market Data Inputs"
        A[Implied Volatility]
        B[Time of Day]
        C[Order Book Depth]
        D[Recent Trade Volume]
    end
    
    subgraph "Fill Model Components"
        E[Base Probability]
        F[Volatility Adjustment]
        G[Liquidity Adjustment]
        H[Profile Adjustment]
    end
    
    subgraph "Execution Simulation"
        I[Fill Decision]
        J[Partial Fill Logic]
        K[Component Fill Generation]
    end
    
    A --> F
    B --> G
    C --> G
    D --> G
    
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J
    J --> K
```

## Execution Engine Architecture

### ComboMatchingEngine Integration

```python
class ComboMatchingEngine(BacktestMatchingEngine):
    """Specialized matching engine for option combo orders."""
    
    def __init__(self, ...):
        super().__init__(...)
        self._combo_fill_model = EnhancedComboFillModel(...)
        self._combo_orders = {}  # Track active combo orders
    
    def match_combo_order(self, order: ComboOrder, quote: QuoteTick) -> list[OrderFilled]:
        """Match combo order against synthetic or native quotes."""
        
        # Calculate fill probability
        fill_prob = self._combo_fill_model.calculate_fill_probability(
            combo=order.instrument,
            order_side=order.side,
            order_price=order.price,
            market_quote=quote,
            market_conditions=self._get_market_conditions()
        )
        
        # Determine if order fills
        if random.random() <= fill_prob:
            return self._generate_combo_fills(order, quote)
        
        return []
    
    def _generate_combo_fills(self, order: ComboOrder, quote: QuoteTick) -> list[OrderFilled]:
        """Generate individual component fills for combo execution."""
        component_fills = []
        
        for leg in order.instrument.legs:
            # Create individual fill for each leg
            leg_fill = OrderFilled(
                trader_id=order.trader_id,
                strategy_id=order.strategy_id,
                instrument_id=leg.instrument_id,
                client_order_id=self._generate_leg_order_id(order.client_order_id, leg),
                venue_order_id=VenueOrderId(str(UUID4())),
                execution_id=ExecutionId(str(UUID4())),
                order_side=self._calculate_leg_side(order.side, leg),
                order_type=order.order_type,
                last_qty=Quantity.from_int(int(order.quantity) * abs(leg.ratio)),
                last_px=self._calculate_leg_price(order.price, leg, quote),
                currency=order.instrument.quote_currency,
                commission=Money(0, order.instrument.quote_currency),
                liquidity_side=LiquiditySide.TAKER,
                ts_event=quote.ts_event,
                ts_init=self._clock.timestamp_ns(),
            )
            component_fills.append(leg_fill)
        
        return component_fills
```

### Order Flow Architecture

```mermaid
sequenceDiagram
    participant Strategy as Trading Strategy
    participant Builder as ComboOrderBuilder
    participant Engine as ComboMatchingEngine
    participant Model as Enhanced Fill Model
    participant Cache as Data Cache

    Strategy->>Builder: Create Combo Order
    Builder->>Builder: Decompose to Leg Orders
    Builder->>Engine: Submit Combo Order
    Engine->>Cache: Get Market Quote
    Engine->>Model: Calculate Fill Probability
    Model->>Engine: Return Probability
    Engine->>Engine: Generate Component Fills
    Engine->>Strategy: Publish Fill Events
```

## Data Flow Differences: Live vs Backtest

### Backtesting Environment

```mermaid
graph TB
    subgraph "Backtest Data Flow"
        A[Historical Leg Quotes] --> B[ComboMarketDataProvider]
        B --> C[Synthetic Combo Quotes]
        C --> D[ComboMatchingEngine]
        D --> E[Enhanced Fill Model]
        E --> F[Simulated Fills]
    end
    
    subgraph "Quote Generation"
        G[Leg Quote A] --> H[Quote Cache]
        I[Leg Quote B] --> H
        H --> J[Check All Legs Available]
        J --> K[Calculate Combo Price]
        K --> L[Generate Synthetic Quote]
    end
```

### Live Trading Environment

```mermaid
graph TB
    subgraph "Live Data Flow"
        A[Broker Native Quotes] --> B[Data Client]
        B --> C[Standard Quote Processing]
        C --> D[Live Execution Engine]
        D --> E[Broker Order Management]
        E --> F[Real Market Fills]
    end
    
    subgraph "Native Quote Handling"
        G[BAG Contract Subscription] --> H[IB Market Data]
        H --> I[Native Combo Quotes]
        I --> J[No Synthetic Generation]
    end
```

### Key Differences

| Aspect | Backtesting | Live Trading |
|--------|-------------|--------------|
| **Quote Source** | Synthetic from legs | Native from broker |
| **Quote Generation** | Active calculation | Passive subscription |
| **Fill Simulation** | Enhanced model | Real market execution |
| **Data Storage** | Optional cataloging | Real-time processing |
| **Latency** | Deterministic | Variable network latency |

## Data Cataloging System

### Storage Architecture

The catalog system can store both synthetic and native combo quotes:

```mermaid
graph LR
    subgraph "Data Sources"
        A[Synthetic Quotes<br/>Backtesting]
        B[Native Quotes<br/>Live Trading]
    end
    
    subgraph "Catalog Storage"
        C[Parquet Files]
        D[Time Partitioning]
        E[Instrument Directories]
    end
    
    subgraph "Query Interface"
        F[Direct ID Lookup]
        G[Registry-Based Search]
        H[Characteristic Filtering]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
```

### Storage Structure

```
data/quotes/
├── SPY240315P00480000.OPRA/                           # Individual legs
├── SPY240315C00520000.OPRA/                           # Individual legs
├── 1SPY240315P00500000/1SPY240315C00500000.OPRA/     # Straddle combo
└── 1SPY240315P00480000/(1)SPY240315P00490000/(1)SPY240315C00510000/1SPY240315C00520000.OPRA/  # Iron Condor
```

### Registry-Based Discovery

Due to complex auto-generated instrument IDs, a registry system enables efficient discovery:

```python
class ComboQuoteRegistry:
    """Registry for combo quote discovery and management."""
    
    def find_combos(
        self,
        underlying: str = None,
        strategy_type: str = None,
        expiry_range: tuple = None,
        strike_range: tuple = None,
    ) -> list[str]:
        """Find combo instrument IDs by characteristics."""
        # Implementation uses multiple indices for fast lookup
```

## Benefits and Use Cases

### Historical Analysis
- **Backtesting**: Replay exact combo market conditions
- **Strategy Development**: Analyze combo spread behavior over time
- **Model Validation**: Compare synthetic vs actual combo pricing

### Research Applications
- **Spread Analysis**: Study combo bid-ask spreads and liquidity patterns
- **Market Microstructure**: Analyze combo vs leg pricing relationships
- **Volatility Studies**: Examine vega-weighted pricing dynamics

### Operational Benefits
- **Audit Trail**: Complete record of combo pricing and execution decisions
- **Debugging**: Replay exact market conditions that caused issues
- **Compliance**: Historical record of combo quote generation and fills

## Implementation Considerations

### Performance Optimization
- **Sparse Data Handling**: Combo quotes generated only when all legs available
- **Efficient Storage**: Parquet compression for time-series data
- **Query Performance**: Index-based lookup and metadata caching

### Scalability Factors
- **Memory Management**: Streaming queries for large datasets
- **Concurrent Access**: Connection pooling for multiple simultaneous queries
- **Storage Growth**: Time-based partitioning and archival strategies

## Advanced Implementation Details

### Component Integration Architecture

```mermaid
graph TB
    subgraph "Core Components"
        A[OptionCombo Instrument]
        B[ComboMarketDataProvider]
        C[ComboMatchingEngine]
        D[Enhanced Fill Model]
    end

    subgraph "Supporting Systems"
        E[Greeks Calculator]
        F[Volatility Surface]
        G[Risk Manager]
        H[Position Manager]
    end

    subgraph "Data Infrastructure"
        I[Quote Cache]
        J[Instrument Cache]
        K[Data Catalog]
        L[Message Bus]
    end

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F

    B --> I
    A --> J
    C --> K
    All --> L
```

### Greeks-Based Pricing Integration

The system integrates with nautilus_trader's Greeks calculator for sophisticated pricing:

```python
class ComboGreeksCalculator:
    """Calculate Greeks for option combo instruments."""

    def calculate_combo_greeks(
        self,
        combo: OptionCombo,
        leg_prices: dict[InstrumentId, Price],
        market_conditions: dict
    ) -> dict:
        """Calculate combined Greeks for the entire combo."""

        combo_greeks = {
            'delta': 0.0,
            'gamma': 0.0,
            'theta': 0.0,
            'vega': 0.0,
            'rho': 0.0
        }

        for leg in combo.legs:
            # Get individual leg Greeks
            leg_greeks = self._greeks_calculator.calculate_greeks(
                instrument=self._instrument_cache[leg.instrument_id],
                underlying_price=market_conditions['underlying_price'],
                volatility=market_conditions['implied_volatility'],
                risk_free_rate=market_conditions['risk_free_rate'],
                time_to_expiry=market_conditions['time_to_expiry']
            )

            # Apply leg ratio to Greeks
            for greek_name, greek_value in leg_greeks.items():
                combo_greeks[greek_name] += leg.ratio * greek_value

        return combo_greeks

    def calculate_vega_weighted_spread(
        self,
        combo: OptionCombo,
        base_spread: float,
        market_conditions: dict
    ) -> float:
        """Calculate vega-weighted bid-ask spread adjustment."""

        combo_greeks = self.calculate_combo_greeks(combo, {}, market_conditions)
        total_vega = abs(combo_greeks['vega'])

        # Apply vega multiplier for spread widening
        vega_adjustment = total_vega * combo.vega_multiplier * base_spread

        return vega_adjustment
```

### Market Microstructure Modeling

```mermaid
sequenceDiagram
    participant Market as Market Data
    participant Model as Fill Model
    participant Conditions as Market Conditions
    participant Decision as Fill Decision

    Market->>Model: Current Quote
    Market->>Conditions: Volatility Surface
    Market->>Conditions: Order Book Depth
    Market->>Conditions: Recent Volume

    Model->>Conditions: Get Market State
    Conditions->>Model: Aggregated Conditions

    Model->>Model: Calculate Base Probability
    Model->>Model: Apply Volatility Adjustment
    Model->>Model: Apply Time-of-Day Factor
    Model->>Model: Apply Execution Profile

    Model->>Decision: Final Fill Probability
    Decision->>Decision: Random Fill Decision
```

### Enhanced Fill Model Components

```python
class MarketMicrostructureModel:
    """Advanced market microstructure modeling for combo fills."""

    def __init__(self):
        self._volatility_curves = {}
        self._liquidity_profiles = {}
        self._execution_profiles = {
            'market_maker': {
                'base_fill_rate': 0.85,
                'price_improvement': 0.15,
                'latency_advantage': True
            },
            'retail': {
                'base_fill_rate': 0.65,
                'price_improvement': 0.05,
                'latency_advantage': False
            },
            'institutional': {
                'base_fill_rate': 0.75,
                'price_improvement': 0.10,
                'latency_advantage': False
            }
        }

    def calculate_volatility_adjustment(
        self,
        combo: OptionCombo,
        current_iv: float,
        historical_iv: float
    ) -> float:
        """Adjust fill probability based on volatility regime."""

        # Higher volatility generally means wider spreads and lower fill rates
        iv_ratio = current_iv / historical_iv if historical_iv > 0 else 1.0

        if iv_ratio > 1.2:  # High volatility regime
            return 0.8  # Reduce fill probability
        elif iv_ratio < 0.8:  # Low volatility regime
            return 1.1  # Increase fill probability
        else:
            return 1.0  # Normal regime

    def calculate_time_of_day_adjustment(self, current_time: datetime) -> float:
        """Adjust fill probability based on market session timing."""

        market_open = current_time.replace(hour=9, minute=30, second=0)
        market_close = current_time.replace(hour=16, minute=0, second=0)

        # Calculate minutes from market open
        minutes_from_open = (current_time - market_open).total_seconds() / 60

        # Liquidity profile: higher at open/close, lower at lunch
        if minutes_from_open < 30:  # First 30 minutes
            return 1.2  # High liquidity
        elif minutes_from_open < 120:  # Next 1.5 hours
            return 1.0  # Normal liquidity
        elif minutes_from_open < 240:  # Lunch period
            return 0.8  # Lower liquidity
        elif minutes_from_open < 360:  # Afternoon
            return 1.0  # Normal liquidity
        else:  # Last 30 minutes
            return 1.3  # High liquidity (closing auction)

    def calculate_execution_profile_adjustment(
        self,
        profile: str,
        order_size: int,
        market_depth: dict
    ) -> float:
        """Adjust based on execution profile and market conditions."""

        profile_config = self._execution_profiles.get(profile, self._execution_profiles['retail'])
        base_rate = profile_config['base_fill_rate']

        # Adjust for order size relative to market depth
        if market_depth:
            total_depth = market_depth.get('bid_size', 0) + market_depth.get('ask_size', 0)
            size_ratio = order_size / max(total_depth, 1)

            if size_ratio > 0.5:  # Large order relative to depth
                return base_rate * 0.7
            elif size_ratio > 0.2:  # Medium order
                return base_rate * 0.9
            else:  # Small order
                return base_rate

        return base_rate
```

### Integration with Interactive Brokers

```python
class IBComboIntegration:
    """Integration layer for Interactive Brokers combo trading."""

    def create_bag_contract(self, combo: OptionCombo) -> Contract:
        """Create IB BAG contract from OptionCombo."""

        bag = Contract()
        bag.symbol = combo.underlying
        bag.secType = "BAG"
        bag.currency = str(combo.quote_currency)
        bag.exchange = "SMART"

        # Create combo legs
        combo_legs = []
        for leg in combo.legs:
            ib_leg = ComboLeg()
            ib_leg.conId = self._get_contract_id(leg.instrument_id)
            ib_leg.ratio = abs(leg.ratio)
            ib_leg.action = "BUY" if leg.ratio > 0 else "SELL"
            ib_leg.exchange = str(leg.instrument_id.venue)
            combo_legs.append(ib_leg)

        bag.comboLegs = combo_legs
        return bag

    def convert_ib_combo_fill(
        self,
        ib_execution: Execution,
        combo: OptionCombo
    ) -> list[OrderFilled]:
        """Convert IB combo fill to individual leg fills."""

        component_fills = []

        # IB provides combo fill - need to decompose to legs
        combo_price = ib_execution.price
        combo_quantity = ib_execution.shares

        for leg in combo.legs:
            # Calculate leg-specific fill details
            leg_quantity = combo_quantity * abs(leg.ratio)
            leg_price = self._calculate_leg_price_from_combo(
                combo_price, leg, combo
            )

            leg_fill = OrderFilled(
                trader_id=self._trader_id,
                strategy_id=self._strategy_id,
                instrument_id=leg.instrument_id,
                client_order_id=self._generate_leg_order_id(ib_execution.orderId, leg),
                venue_order_id=VenueOrderId(str(ib_execution.execId)),
                execution_id=ExecutionId(f"{ib_execution.execId}_{leg.instrument_id}"),
                order_side=OrderSide.BUY if leg.ratio > 0 else OrderSide.SELL,
                order_type=OrderType.MARKET,  # Combo executed as market
                last_qty=Quantity.from_int(leg_quantity),
                last_px=Price.from_str(str(leg_price)),
                currency=combo.quote_currency,
                commission=self._calculate_leg_commission(ib_execution.commission, leg, combo),
                liquidity_side=LiquiditySide.TAKER,
                ts_event=UnixNanos.from_datetime(ib_execution.time),
                ts_init=self._clock.timestamp_ns(),
            )

            component_fills.append(leg_fill)

        return component_fills
```

### Performance Monitoring and Analytics

```mermaid
graph TB
    subgraph "Performance Metrics"
        A[Fill Rate Analysis]
        B[Price Improvement Tracking]
        C[Latency Measurements]
        D[Slippage Analysis]
    end

    subgraph "Market Analysis"
        E[Volatility Impact]
        F[Time-of-Day Effects]
        G[Liquidity Patterns]
        H[Spread Analysis]
    end

    subgraph "Model Validation"
        I[Predicted vs Actual Fills]
        J[Greeks Accuracy]
        K[Risk Metrics]
        L[P&L Attribution]
    end

    A --> I
    B --> J
    C --> K
    D --> L
    E --> I
    F --> J
    G --> K
    H --> L
```

### Testing and Validation Framework

```python
class ComboSystemValidator:
    """Comprehensive testing framework for combo system."""

    def validate_instrument_id_generation(self):
        """Test deterministic ID generation."""
        # Test cases for various combo types
        test_cases = [
            {
                'name': 'Iron Condor',
                'legs': [...],
                'expected_id': '1SPY240315P00480000/(1)SPY240315P00490000/...'
            },
            # More test cases
        ]

        for case in test_cases:
            generated_id = generate_combo_instrument_id(case['legs'], self._instrument_cache)
            assert str(generated_id) == case['expected_id']

    def validate_fill_model_accuracy(self, historical_data: dict):
        """Validate fill model against historical execution data."""

        predictions = []
        actuals = []

        for combo_order, market_conditions in historical_data.items():
            predicted_prob = self._fill_model.calculate_fill_probability(
                combo_order.instrument,
                combo_order.side,
                combo_order.price,
                market_conditions['quote'],
                market_conditions
            )

            actual_filled = market_conditions['execution_result']['filled']

            predictions.append(predicted_prob)
            actuals.append(1.0 if actual_filled else 0.0)

        # Calculate model accuracy metrics
        accuracy = self._calculate_prediction_accuracy(predictions, actuals)
        return accuracy

    def validate_quote_generation_consistency(self):
        """Test synthetic quote generation consistency."""

        # Test that same leg quotes always produce same combo quote
        leg_quotes = self._create_test_leg_quotes()

        quote1 = self._combo_provider.generate_combo_quote(self._test_combo, leg_quotes)
        quote2 = self._combo_provider.generate_combo_quote(self._test_combo, leg_quotes)

        assert quote1.bid_price == quote2.bid_price
        assert quote1.ask_price == quote2.ask_price
        assert quote1.ts_event == quote2.ts_event
```

## Conclusion

The option combo architecture in nautilus_trader provides a comprehensive solution for multi-leg option strategy trading. Key architectural strengths include:

1. **Deterministic Instrument Modeling**: Auto-generated IDs ensure consistency across all system components
2. **Flexible Quote Generation**: Supports both synthetic backtesting quotes and native live broker quotes
3. **Sophisticated Fill Simulation**: Enhanced models incorporating volatility, time-of-day, and execution profiles
4. **Comprehensive Data Management**: Full cataloging capabilities with registry-based discovery
5. **Seamless Integration**: Works with existing nautilus_trader infrastructure including Greeks calculation
6. **Robust Testing Framework**: Comprehensive validation for all system components
7. **Production-Ready**: Full Interactive Brokers integration with BAG contract support

This architecture enables sophisticated option trading strategies with realistic backtesting, comprehensive historical analysis, and robust live trading capabilities. The system's modular design allows for easy extension and customization while maintaining high performance and reliability standards.
