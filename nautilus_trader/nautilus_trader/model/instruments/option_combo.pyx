# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

from decimal import Decimal

import pandas as pd
import pytz

from libc.stdint cimport uint64_t

from nautilus_trader.core.correctness cimport Condition
from nautilus_trader.core.datetime cimport format_iso8601
from nautilus_trader.core.rust.model cimport AssetClass
from nautilus_trader.core.rust.model cimport InstrumentClass
from nautilus_trader.core.rust.model cimport OrderSide
from nautilus_trader.model.functions cimport asset_class_from_str
from nautilus_trader.model.functions cimport asset_class_to_str
from nautilus_trader.model.functions cimport instrument_class_from_str
from nautilus_trader.model.functions cimport instrument_class_to_str
from nautilus_trader.model.functions cimport order_side_from_str
from nautilus_trader.model.functions cimport order_side_to_str
from nautilus_trader.model.identifiers cimport InstrumentId
from nautilus_trader.model.identifiers cimport Symbol
from nautilus_trader.model.instruments.base cimport Instrument
from nautilus_trader.model.instruments.base cimport Price
from nautilus_trader.model.objects cimport Currency
from nautilus_trader.model.objects cimport Quantity


cdef class ComboLeg:
    """
    Represents a single leg within an option combo.

    Parameters
    ----------
    instrument_id : InstrumentId
        The instrument ID for this leg.
    ratio : int
        The signed ratio/quantity multiplier for this leg.
        Positive values indicate BUY, negative values indicate SELL.
    """



    def __init__(
        self,
        InstrumentId instrument_id not None,
        int ratio,
    ) -> None:
        Condition.not_equal(ratio, 0, "ratio", "zero")

        self.instrument_id = instrument_id
        self.ratio = ratio

    @property
    def side(self) -> OrderSide:
        """Return the order side based on ratio sign."""
        return OrderSide.BUY if self.ratio > 0 else OrderSide.SELL

    @property
    def abs_ratio(self) -> int:
        """Return the absolute value of the ratio."""
        return abs(self.ratio)

    def __repr__(self) -> str:
        return (
            f"{type(self).__name__}("
            f"instrument_id={self.instrument_id}, "
            f"ratio={self.ratio})"
        )

    def __str__(self) -> str:
        side_str = "BUY" if self.ratio > 0 else "SELL"
        return f"{side_str} {abs(self.ratio)} {self.instrument_id}"


cdef class OptionCombo(Instrument):
    """
    Represents a multi-leg option combination instrument (2-4 legs).

    This instrument type allows trading of option combinations like iron condors,
    butterflies, straddles, etc. The combo is priced as a linear combination of
    its component legs, with bid/ask spreads based on vega-weighted sizing.

    The instrument ID is automatically generated based on the sorted combo legs
    using the format: 1{symbol1}/(2){symbol2}.venue where positive weights are
    shown as numbers and negative weights are shown in parentheses.

    Parameters
    ----------
    raw_symbol : Symbol
        The raw/local/native symbol for the instrument.
    asset_class : AssetClass
        The option combo asset class.
    currency : Currency
        The combo currency.
    price_precision : int
        The price decimal precision.
    price_increment : Price
        The minimum price increment (tick size).
    multiplier : Quantity
        The combo multiplier.
    lot_size : Quantity
        The rounded lot unit size.
    underlying : str
        The underlying asset.
    strategy_type : str
        The strategy type (e.g., "IRON_CONDOR", "BUTTERFLY", "STRADDLE").
    legs : list[ComboLeg]
        The component legs (2-4 legs). Legs are automatically sorted by:
        Put before Call, then descending expiry, then ascending strikes.
    instrument_cache : dict[InstrumentId, OptionContract], optional
        Cache of option instruments used to retrieve strike, expiry, and option type
        information for sorting and instrument ID generation. If None, a simple
        instrument ID will be generated.
    vega_multiplier : float, default 0.0
        Multiplier for vega-based bid/ask spread calculation.
    activation_ns : uint64, default 0
        UNIX timestamp (nanoseconds) for when the instrument becomes active.
    expiration_ns : uint64, default 0
        UNIX timestamp (nanoseconds) for when the instrument expires.
    ts_event : uint64, default 0
        UNIX timestamp (nanoseconds) when the data event occurred.
    ts_init : uint64, default 0
        UNIX timestamp (nanoseconds) when the object was initialized.
    instrument_id : InstrumentId, optional
        The instrument ID. If provided, this will be used instead of generating
        one automatically. Used primarily for deserialization.
    activation_ns : uint64_t
        UNIX timestamp (nanoseconds) for contract activation.
    expiration_ns : uint64_t
        UNIX timestamp (nanoseconds) for contract expiration.
    ts_event : uint64_t
        UNIX timestamp (nanoseconds) when the data event occurred.
    ts_init : uint64_t
        UNIX timestamp (nanoseconds) when the data object was initialized.
    margin_init : Decimal, optional
        The initial margin requirement.
    margin_maint : Decimal, optional
        The maintenance margin requirement.
    maker_fee : Decimal, optional
        The maker fee rate.
    taker_fee : Decimal, optional
        The taker fee rate.
    exchange : str, optional
        The exchange MIC code.
    info : dict, optional
        Additional instrument information.

    Raises
    ------
    ValueError
        If legs list is empty or has more than 4 legs.
    ValueError
        If strategy_type is not a valid string.
    ValueError
        If vega_multiplier is not positive.
    ValueError
        If legs have different venues.
    ValueError
        If option symbols cannot be parsed.
    """



    def __init__(
        self,
        Symbol raw_symbol not None,
        AssetClass asset_class,
        Currency currency not None,
        int price_precision,
        Price price_increment not None,
        Quantity multiplier not None,
        Quantity lot_size not None,
        str underlying,
        str strategy_type,
        list legs not None,
        dict instrument_cache = None,
        double vega_multiplier = 0.0,
        uint64_t activation_ns = 0,
        uint64_t expiration_ns = 0,
        uint64_t ts_event = 0,
        uint64_t ts_init = 0,
        InstrumentId instrument_id = None,
        margin_init: Decimal | None = None,
        margin_maint: Decimal | None = None,
        maker_fee: Decimal | None = None,
        taker_fee: Decimal | None = None,
        str exchange = None,
        dict info = None,
    ) -> None:
        # Validation
        Condition.valid_string(underlying, "underlying")
        Condition.valid_string(strategy_type, "strategy_type")
        Condition.not_empty(legs, "legs")
        Condition.is_true(len(legs) >= 2, "legs must have at least 2 elements")
        Condition.is_true(len(legs) <= 4, "legs must have at most 4 elements")
        if vega_multiplier > 0:
            Condition.positive(vega_multiplier, "vega_multiplier")

        if exchange is not None:
            Condition.valid_string(exchange, "exchange")

        # Validate all legs are ComboLeg instances
        for leg in legs:
            Condition.type(leg, ComboLeg, "leg")

        # Use provided instrument_id or generate one automatically
        cdef InstrumentId final_instrument_id = generate_combo_instrument_id(legs, instrument_cache)

        super().__init__(
            instrument_id=final_instrument_id,
            raw_symbol=raw_symbol,
            asset_class=asset_class,
            instrument_class=InstrumentClass.OPTION_SPREAD,  # Reuse existing class
            quote_currency=currency,
            is_inverse=False,
            price_precision=price_precision,
            size_precision=0,  # No fractional contracts
            price_increment=price_increment,
            size_increment=Quantity.from_int_c(1),
            multiplier=multiplier,
            lot_size=lot_size,
            max_quantity=None,
            min_quantity=Quantity.from_int_c(1),
            max_notional=None,
            min_notional=None,
            max_price=None,
            min_price=None,
            margin_init=margin_init or Decimal(0),
            margin_maint=margin_maint or Decimal(0),
            maker_fee=maker_fee or Decimal(0),
            taker_fee=taker_fee or Decimal(0),
            ts_event=ts_event,
            ts_init=ts_init,
        )

        self.underlying = underlying
        self.strategy_type = strategy_type
        self.legs = legs
        self.vega_multiplier = vega_multiplier
        self.activation_ns = activation_ns
        self.expiration_ns = expiration_ns
        self.exchange = exchange
        self.info = info or {}

    @property
    def activation(self):
        """
        Return the activation datetime (UTC).

        Returns
        -------
        pd.Timestamp
        """
        return pd.Timestamp(self.activation_ns, tz=pytz.UTC)

    @property
    def expiration(self):
        """
        Return the expiration datetime (UTC).

        Returns
        -------
        pd.Timestamp
        """
        return pd.Timestamp(self.expiration_ns, tz=pytz.UTC)

    @property
    def is_expired(self):
        """
        Return whether the combo is expired.

        Returns
        -------
        bool
        """
        return self.expiration_ns <= pd.Timestamp.utcnow().value

    def get_leg_instrument_ids(self):
        """
        Return the instrument IDs of all legs.

        Returns
        -------
        list[InstrumentId]
        """
        return [leg.instrument_id for leg in self.legs]

    def calculate_combo_price(self, leg_prices: dict):
        """
        Calculate the combo price as a linear combination of leg prices.

        Parameters
        ----------
        leg_prices : dict[InstrumentId, Price]
            Dictionary mapping leg instrument IDs to their current prices.

        Returns
        -------
        Price or None
            The calculated combo price, or None if any leg price is missing.
        """
        if not leg_prices:
            return None

        combo_value = 0.0
        for leg in self.legs:
            leg_price = leg_prices.get(leg.instrument_id)
            if leg_price is None:
                return None
            combo_value += leg.ratio * float(leg_price)

        return Price.from_str_c(f"{combo_value:.{self.price_precision}f}")

    def calculate_vega_weighted_spread(self, leg_vegas: dict, base_spread: float = 0.01, vega_multiplier: float = None):
        """
        Calculate bid/ask spread based on vega-weighted sizing.

        Parameters
        ----------
        leg_vegas : dict[InstrumentId, float]
            Dictionary mapping leg instrument IDs to their vega values.
        base_spread : float, default 0.01
            Base spread percentage to apply.
        vega_multiplier : float, optional
            Override vega multiplier. If None, uses the combo's vega_multiplier.

        Returns
        -------
        float
            The calculated spread multiplier.
        """
        if not leg_vegas:
            return base_spread

        total_vega = 0.0
        for leg in self.legs:
            leg_vega = leg_vegas.get(leg.instrument_id, 0.0)
            total_vega += abs(leg.ratio * leg_vega)

        # Use provided vega_multiplier or fall back to combo's default
        multiplier = vega_multiplier if vega_multiplier is not None else self.vega_multiplier
        return base_spread * (1.0 + total_vega * multiplier)

    def __repr__(self) -> str:
        return (
            f"{type(self).__name__}("
            f"id={self.id}, "
            f"underlying={self.underlying}, "
            f"strategy_type={self.strategy_type}, "
            f"legs={len(self.legs)})"
        )

    def __str__(self) -> str:
        legs_str = ", ".join(str(leg) for leg in self.legs)
        return f"{self.strategy_type} {self.underlying} [{legs_str}]"

    @staticmethod
    cdef OptionCombo from_dict_c(dict values, dict instrument_cache = None):
        """
        Return an option combo from the given initialization values.

        Parameters
        ----------
        values : dict[str, object]
            The values for initialization.
        instrument_cache : dict[InstrumentId, OptionContract], optional
            Cache of option instruments. If None, will create a minimal cache
            from the stored instrument ID (for backward compatibility).

        Returns
        -------
        OptionCombo
        """
        # Parse legs
        legs_data = values["legs"]
        legs = []
        for leg_data in legs_data:
            leg = ComboLeg(
                instrument_id=InstrumentId.from_str_c(leg_data["instrument_id"]),
                ratio=leg_data["ratio"],
            )
            legs.append(leg)

        # Handle instrument_cache for backward compatibility
        if instrument_cache is None:
            # For backward compatibility, create a minimal cache
            # This won't work for automatic ID generation, but allows deserialization
            instrument_cache = {}

        return OptionCombo(
            raw_symbol=Symbol(values["raw_symbol"]),
            asset_class=asset_class_from_str(values["asset_class"]),
            currency=Currency.from_str_c(values["quote_currency"]),
            price_precision=values["price_precision"],
            price_increment=Price.from_str_c(values["price_increment"]),
            multiplier=Quantity.from_str_c(values["multiplier"]),
            lot_size=Quantity.from_str_c(values["lot_size"]),
            underlying=values["underlying"],
            strategy_type=values["strategy_type"],
            legs=legs,
            instrument_cache=instrument_cache,
            vega_multiplier=values["vega_multiplier"],
            activation_ns=values["activation_ns"],
            expiration_ns=values["expiration_ns"],
            ts_event=values["ts_event"],
            ts_init=values["ts_init"],
            instrument_id=InstrumentId.from_str_c(values["id"]),  # Use stored instrument_id
            margin_init=Decimal(values["margin_init"]) if values.get("margin_init") is not None else None,
            margin_maint=Decimal(values["margin_maint"]) if values.get("margin_maint") is not None else None,
            maker_fee=Decimal(values["maker_fee"]) if values.get("maker_fee") is not None else None,
            taker_fee=Decimal(values["taker_fee"]) if values.get("taker_fee") is not None else None,
            exchange=values.get("exchange"),
            info=values.get("info"),
        )

    @staticmethod
    def from_dict(dict values, dict instrument_cache = None) -> OptionCombo:
        """
        Return an option combo from the given initialization values.

        Parameters
        ----------
        values : dict[str, object]
            The values for initialization.
        instrument_cache : dict[InstrumentId, OptionContract], optional
            Cache of option instruments. If None, will create a minimal cache
            from the stored instrument ID (for backward compatibility).

        Returns
        -------
        OptionCombo
        """
        return OptionCombo.from_dict_c(values, instrument_cache)

    cpdef dict to_dict(self):
        """
        Return a dictionary representation of this instrument.

        Returns
        -------
        dict[str, object]
        """
        legs_data = []
        for leg in self.legs:
            legs_data.append({
                "instrument_id": str(leg.instrument_id),
                "ratio": leg.ratio,  # Now stores signed ratio directly
            })

        return {
            "type": "OptionCombo",
            "id": str(self.id),
            "raw_symbol": str(self.raw_symbol),
            "asset_class": asset_class_to_str(self.asset_class),
            "quote_currency": str(self.quote_currency),
            "is_inverse": self.is_inverse,
            "price_precision": self.price_precision,
            "price_increment": str(self.price_increment),
            "multiplier": str(self.multiplier),
            "lot_size": str(self.lot_size),
            "underlying": self.underlying,
            "strategy_type": self.strategy_type,
            "legs": legs_data,
            "vega_multiplier": self.vega_multiplier,
            "activation_ns": self.activation_ns,
            "expiration_ns": self.expiration_ns,
            "ts_event": self.ts_event,
            "ts_init": self.ts_init,
            "margin_init": str(self.margin_init) if self.margin_init else None,
            "margin_maint": str(self.margin_maint) if self.margin_maint else None,
            "maker_fee": str(self.maker_fee) if self.maker_fee else None,
            "taker_fee": str(self.taker_fee) if self.taker_fee else None,
            "exchange": self.exchange,
            "info": self.info,
        }


cdef InstrumentId generate_combo_instrument_id(list legs, dict instrument_cache):
    """
    Generate the combo instrument ID based on sorted legs.
    Format: 1{symbol1}/(2){symbol2}.venue

    Parameters
    ----------
    legs : list[ComboLeg]
        The combo legs (will be sorted internally).
    instrument_cache : dict[InstrumentId, OptionContract]
        Cache of option instruments to get strike, expiry, and option type.

    Returns
    -------
    InstrumentId
        The generated combo instrument ID.

    Raises
    ------
    ValueError
        If legs have different venues or if instruments not found in cache.
    """
    if not legs:
        raise ValueError("Cannot generate combo ID from empty legs")

    # Sort the legs
    sorted_legs = sort_combo_legs(legs, instrument_cache)

    # Check that all legs have the same venue
    cdef str venue = str(sorted_legs[0].instrument_id.venue)

    for leg in sorted_legs[1:]:
        if str(leg.instrument_id.venue) != venue:
            raise ValueError(f"All legs must have the same venue, found: {venue} and {str(leg.instrument_id.venue)}")

    # Build the combo symbol using instrument information
    cdef list symbol_parts = []

    for leg in sorted_legs:
        instrument = instrument_cache[leg.instrument_id]
        ratio_str = str(abs(leg.ratio)) if leg.ratio > 0 else f"({abs(leg.ratio)})"
        symbol_parts.append(f"{ratio_str}{instrument.id.symbol}")

    combo_symbol = "/".join(symbol_parts)

    return InstrumentId.from_str_c(f"{combo_symbol}.{venue}")


cdef list sort_combo_legs(list legs, dict instrument_cache):
    """
    Sort combo legs according to the specified order:
    1. Put before Call
    2. Descending expiry (later expiry first)
    3. Ascending strikes (lower strike first)

    Parameters
    ----------
    legs : list[ComboLeg]
        The combo legs to sort.
    instrument_cache : dict[InstrumentId, OptionContract]
        Cache of option instruments to get strike, expiry, and option type.

    Returns
    -------
    list[ComboLeg]
        The sorted combo legs.

    Raises
    ------
    ValueError
        If any leg instrument is not found in cache or is not an OptionContract.
    """
    cdef list leg_instrument_pairs = []

    # Get instrument objects for each leg
    for leg in legs:
        instrument = instrument_cache.get(leg.instrument_id)
        if instrument is None:
            raise ValueError(f"Instrument not found in cache: {leg.instrument_id}")

        # Check if it's an OptionContract (has the required attributes)
        if not hasattr(instrument, 'option_kind') or not hasattr(instrument, 'strike_price') or not hasattr(instrument, 'expiration_ns'):
            raise ValueError(f"Instrument is not an OptionContract: {leg.instrument_id}")

        leg_instrument_pairs.append((leg, instrument))

    # Sort by: Put before Call, descending expiry, ascending strikes
    leg_instrument_pairs.sort(key=lambda x: (
        0 if str(x[1].option_kind) == 'PUT' else 1,  # Put before Call
        -x[1].expiration_ns,  # Descending expiry (negative for reverse order)
        float(x[1].strike_price)  # Ascending strikes
    ))

    return [pair[0] for pair in leg_instrument_pairs]
