import numpy as np
from numba import jit
from math import erf
from scipy.stats import norm



# https://github.com/yixuan/fastncdf
def aproximate_function(exact_func, last_x, f2_norm_inf, fast_fn_extrapolation_value, fn_epsilon):
    # requires the last point of an interval on which to interpolate, the norm inf of the second derivative of the function, the extrapolation constant, and the e precision
    fast_fn_h = np.sqrt(8.0 / f2_norm_inf * fn_epsilon)
    fast_fn_xs = np.arange(0.0, last_x + fast_fn_h, fast_fn_h)
    fast_fn_ys = np.array([exact_func(x) for x in fast_fn_xs])
    fast_fn_hinv = 1.0 / fast_fn_h
    fast_fn_max_x = fast_fn_xs[-1] - 1e-10  # we subtract a small number here to make sure int conversion below never sends last vector index

    return fast_fn_hinv, fast_fn_max_x, fast_fn_ys, fast_fn_extrapolation_value


@jit(cache=True)
def fast_fn_pos(x, fast_fn_hinv, fast_fn_max_x, fast_fn_ys, fast_fn_extrapolation_value):
    if x > fast_fn_max_x:
        return fast_fn_extrapolation_value

    w0 = x * fast_fn_hinv
    i = int(w0)
    w = w0 - i

    return fast_fn_ys[i] + w * (fast_fn_ys[i + 1] - fast_fn_ys[i])


@jit(cache=True)
def exact_norm_cdf(x):
    return 0.5 * (1.0 + erf(x / np.sqrt(2.0)))


cdf_epsilon = 1e-8
cdf_last_x, cdf_f2_norm_inf, cdf_extrapolation_value = (
    norm.ppf(1.0 - cdf_epsilon),
    norm.pdf(1),
    1.0,
)
fast_cdf_hinv, fast_cdf_max_x, fast_cdf_ys, fast_cdf_extrapolation_value = aproximate_function(
    norm.cdf, cdf_last_x, cdf_f2_norm_inf, cdf_extrapolation_value, cdf_epsilon
)


@jit(cache=True)
def fast_norm_cdf(x):
    result = fast_fn_pos(abs(x), fast_cdf_hinv, fast_cdf_max_x, fast_cdf_ys, fast_cdf_extrapolation_value)

    if x < 0:
        result = 1.0 - result

    return result


# test_randn = np.random.randn(100000)
# np.abs(np.array([cl.fast_norm_cdf(x) for x in test_randn]) - norm.cdf(test_randn)).max()
# %timeit norm.cdf(0.2354)
# %timeit cl.fast_norm_cdf(0.2354)


use_fast_fn = True


@jit(cache=True)
def norm_cdf(x, use_fast_fn=use_fast_fn):
    if use_fast_fn:
        return fast_norm_cdf(x)

    return exact_norm_cdf(x)


@jit(cache=True)
def exact_norm_dist(x):
    # 1. / np.sqrt(2. * np.pi)
    return 0.3989422804014327 * np.exp(-0.5 * x**2)


# for an approximate linear norm dist function norm inf of f'' is 1 / sqrt(2 pi) (instead of norm.pdf(1) for norm_cdf)
# f(x) = eps implies x = sqrt(- 2 log(sqrt(2 pi) eps)) (instead of norm.ppf(1. - cdf_epsilon) for norm_cdf)
norm_dist_epsilon = 1e-8
norm_dist_last_x, norm_dist_f2_norm_inf, norm_dist_extrapolation_value = (
    np.sqrt(-2 * np.log(np.sqrt(2 * np.pi) * norm_dist_epsilon)),
    1 / np.sqrt(2 * np.pi),
    0.0,
)
(
    fast_norm_dist_hinv,
    fast_norm_dist_max_x,
    fast_norm_dist_ys,
    fast_norm_dist_extrapolation_value,
) = aproximate_function(
    exact_norm_dist,
    norm_dist_last_x,
    norm_dist_f2_norm_inf,
    norm_dist_extrapolation_value,
    norm_dist_epsilon,
)


@jit(cache=True)
def fast_norm_dist(x):
    return fast_fn_pos(
        abs(x),
        fast_norm_dist_hinv,
        fast_norm_dist_max_x,
        fast_norm_dist_ys,
        fast_norm_dist_extrapolation_value,
    )


# test_randn = np.random.randn(100000)
# np.abs(np.array([cl.fast_norm_dist(x) for x in test_randn]) - np.array([cl.exact_norm_dist(x) for x in test_randn])).max()
# %timeit cl.exact_norm_dist(0.2354)
# %timeit cl.fast_norm_dist(0.2354)


@jit(cache=True)
def norm_dist(x, use_fast_fn=use_fast_fn):
    # exact norm is faster in python than fast one, but not in c++
    if use_fast_fn:
        return fast_norm_dist(x)

    return exact_norm_dist(x)