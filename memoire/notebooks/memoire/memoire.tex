% Options for packages loaded elsewhere
% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
\PassOptionsToPackage{dvipsnames,svgnames,x11names}{xcolor}
%
\documentclass[
]{article}
\usepackage{xcolor}
\usepackage{amsmath,amssymb}
\setcounter{secnumdepth}{5}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
  \setmainfont[]{Times New Roman}
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
% Make \paragraph and \subparagraph free-standing
\makeatletter
\ifx\paragraph\undefined\else
  \let\oldparagraph\paragraph
  \renewcommand{\paragraph}{
    \@ifstar
      \xxxParagraphStar
      \xxxParagraphNoStar
  }
  \newcommand{\xxxParagraphStar}[1]{\oldparagraph*{#1}\mbox{}}
  \newcommand{\xxxParagraphNoStar}[1]{\oldparagraph{#1}\mbox{}}
\fi
\ifx\subparagraph\undefined\else
  \let\oldsubparagraph\subparagraph
  \renewcommand{\subparagraph}{
    \@ifstar
      \xxxSubParagraphStar
      \xxxSubParagraphNoStar
  }
  \newcommand{\xxxSubParagraphStar}[1]{\oldsubparagraph*{#1}\mbox{}}
  \newcommand{\xxxSubParagraphNoStar}[1]{\oldsubparagraph{#1}\mbox{}}
\fi
\makeatother

\usepackage{color}
\usepackage{fancyvrb}
\newcommand{\VerbBar}{|}
\newcommand{\VERB}{\Verb[commandchars=\\\{\}]}
\DefineVerbatimEnvironment{Highlighting}{Verbatim}{commandchars=\\\{\}}
% Add ',fontsize=\small' for more characters per line
\usepackage{framed}
\definecolor{shadecolor}{RGB}{241,243,245}
\newenvironment{Shaded}{\begin{snugshade}}{\end{snugshade}}
\newcommand{\AlertTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\AnnotationTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\AttributeTok}[1]{\textcolor[rgb]{0.40,0.45,0.13}{#1}}
\newcommand{\BaseNTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\BuiltInTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\CharTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\CommentTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\CommentVarTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{\textit{#1}}}
\newcommand{\ConstantTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\ControlFlowTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{\textbf{#1}}}
\newcommand{\DataTypeTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\DecValTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\DocumentationTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{\textit{#1}}}
\newcommand{\ErrorTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\ExtensionTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\FloatTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\FunctionTok}[1]{\textcolor[rgb]{0.28,0.35,0.67}{#1}}
\newcommand{\ImportTok}[1]{\textcolor[rgb]{0.00,0.46,0.62}{#1}}
\newcommand{\InformationTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\KeywordTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{\textbf{#1}}}
\newcommand{\NormalTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\OperatorTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\OtherTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\PreprocessorTok}[1]{\textcolor[rgb]{0.68,0.00,0.00}{#1}}
\newcommand{\RegionMarkerTok}[1]{\textcolor[rgb]{0.00,0.23,0.31}{#1}}
\newcommand{\SpecialCharTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{#1}}
\newcommand{\SpecialStringTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\StringTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\VariableTok}[1]{\textcolor[rgb]{0.07,0.07,0.07}{#1}}
\newcommand{\VerbatimStringTok}[1]{\textcolor[rgb]{0.13,0.47,0.30}{#1}}
\newcommand{\WarningTok}[1]{\textcolor[rgb]{0.37,0.37,0.37}{\textit{#1}}}

\usepackage{longtable,booktabs,array}
\usepackage{calc} % for calculating minipage widths
% Correct order of tables after \paragraph or \subparagraph
\usepackage{etoolbox}
\makeatletter
\patchcmd\longtable{\par}{\if@noskipsec\mbox{}\fi\par}{}{}
\makeatother
% Allow footnotes in longtable head/foot
\IfFileExists{footnotehyper.sty}{\usepackage{footnotehyper}}{\usepackage{footnote}}
\makesavenoteenv{longtable}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother


% definitions for citeproc citations
\NewDocumentCommand\citeproctext{}{}
\NewDocumentCommand\citeproc{mm}{%
  \begingroup\def\citeproctext{#2}\cite{#1}\endgroup}
\makeatletter
 % allow citations to break across lines
 \let\@cite@ofmt\@firstofone
 % avoid brackets around text for \cite:
 \def\@biblabel#1{}
 \def\@cite#1#2{{#1\if@tempswa , #2\fi}}
\makeatother
\newlength{\cslhangindent}
\setlength{\cslhangindent}{1.5em}
\newlength{\csllabelwidth}
\setlength{\csllabelwidth}{3em}
\newenvironment{CSLReferences}[2] % #1 hanging-indent, #2 entry-spacing
 {\begin{list}{}{%
  \setlength{\itemindent}{0pt}
  \setlength{\leftmargin}{0pt}
  \setlength{\parsep}{0pt}
  % turn on hanging indent if param 1 is 1
  \ifodd #1
   \setlength{\leftmargin}{\cslhangindent}
   \setlength{\itemindent}{-1\cslhangindent}
  \fi
  % set entry spacing
  \setlength{\itemsep}{#2\baselineskip}}}
 {\end{list}}
\usepackage{calc}
\newcommand{\CSLBlock}[1]{\hfill\break\parbox[t]{\linewidth}{\strut\ignorespaces#1\strut}}
\newcommand{\CSLLeftMargin}[1]{\parbox[t]{\csllabelwidth}{\strut#1\strut}}
\newcommand{\CSLRightInline}[1]{\parbox[t]{\linewidth - \csllabelwidth}{\strut#1\strut}}
\newcommand{\CSLIndent}[1]{\hspace{\cslhangindent}#1}



\setlength{\emergencystretch}{3em} % prevent overfull lines

\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}



 


% LaTeX preamble - include required packages
\usepackage{pdfpages}
\makeatletter
\@ifpackageloaded{caption}{}{\usepackage{caption}}
\AtBeginDocument{%
\ifdefined\contentsname
  \renewcommand*\contentsname{Table of contents}
\else
  \newcommand\contentsname{Table of contents}
\fi
\ifdefined\listfigurename
  \renewcommand*\listfigurename{List of Figures}
\else
  \newcommand\listfigurename{List of Figures}
\fi
\ifdefined\listtablename
  \renewcommand*\listtablename{List of Tables}
\else
  \newcommand\listtablename{List of Tables}
\fi
\ifdefined\figurename
  \renewcommand*\figurename{Figure}
\else
  \newcommand\figurename{Figure}
\fi
\ifdefined\tablename
  \renewcommand*\tablename{Table}
\else
  \newcommand\tablename{Table}
\fi
}
\@ifpackageloaded{float}{}{\usepackage{float}}
\floatstyle{ruled}
\@ifundefined{c@chapter}{\newfloat{codelisting}{h}{lop}}{\newfloat{codelisting}{h}{lop}[chapter]}
\floatname{codelisting}{Listing}
\newcommand*\listoflistings{\listof{codelisting}{List of Listings}}
\makeatother
\makeatletter
\makeatother
\makeatletter
\@ifpackageloaded{caption}{}{\usepackage{caption}}
\@ifpackageloaded{subcaption}{}{\usepackage{subcaption}}
\makeatother
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  colorlinks=true,
  linkcolor={blue},
  filecolor={Maroon},
  citecolor={blue},
  urlcolor={blue},
  pdfcreator={LaTeX via pandoc}}


\author{}
\date{}
\begin{document}


\begin{titlepage}
\centering
\vspace*{1.5cm}

{\LARGE\textbf{KEDGE Business School}}\\[0.5cm]
{\large ESC Programme Grande Ecole}\\[1.5cm]

{\Huge\textbf{Overview of Financial Pricing Methods}}\\[1cm]
{\Large A Master's Thesis}\\[2cm]

{\large Presented by:}\\
{\Large\textbf{Sofiane Aberkane}}\\[1.5cm]

{\large Under the supervision of:}\\
{\Large\textbf{Dr. Edward Sun, Professor of Data Science and FinTech}}\\[2cm]

{\large Academic Year 2024-2025}\\[0.5cm]
{\large \today}

\end{titlepage}
\newpage

\section*{Acknowledgments}
\addcontentsline{toc}{section}{Acknowledgments}

\hfill\break

I would like to express my gratitude to my Master's thesis tutor
Dr.~Edward SUN, Professor of Data Science and FinTech at KEDGE Business
School, for accepting the subject of my ESC PGE final year dissertation
(``Overview of Financial Pricing Methods'') and supervising its
delivery.

I also thank my work colleagues for the fruitful discussions on this
subject during my V.I.E assignment at Amundi Asset Management in Munich.

\newpage

\tableofcontents
\newpage

\section{Introduction}\label{introduction}

Financial markets include many different types of assets that investors
and institutions use for risk management or speculation. Understanding
how these assets are priced is important in order to make informed
financial decisions. Indeed, accurate pricing is important because it
allows market participants to know exactly how much their investments
are worth and in some case how to hedge them.

The goal of this paper is to provide an overview of how these financial
instruments are priced, linking theoretical models to real market data
as well as how they can be used in practice.

We aim to answer the question of how financial instruments are priced.

To address this question, we will focus on a selection of several
important financial instruments, including both exchange-traded and
over-the-counter (OTC) instruments, used in today's markets:

\begin{itemize}
\tightlist
\item
  Stocks
\item
  Exchange-traded funds (ETFs)
\item
  Bonds
\item
  Options
\item
  Interest rate swaps
\item
  Inflation swaps
\item
  Swaptions
\end{itemize}

We first provide a literature review of the theory behind several
pricing models before moving on to a research/implementation section.
The classification used in the literature review is based on a two-level
grouping: first by asset class (Equities, Fixed Income, and
Derivatives), and then by instrument type within each asset class.

The research/implementation section will involve extracting market
prices and market data from Bloomberg and researching how to implement
pricers and recalculate prices in Python based on previously presented
pricing models. The process of retrieving data from Bloomberg will also
be documented with screenshots and descriptions of the functions used to
extract the information.

\section{Literature Review}\label{literature-review}

The literature review section aims to provide an overview of the main
theoretical pricing models for the in-scope instruments, as well as
theoretical key concepts applied in financial asset pricing and is based
on books, academic articles, research papers, and asset management
company websites. For each reference cited, the relevant page ranges are
provided. All references used in this paper are listed in the
Bibliography section at the end of it.

\subsection{Equities}\label{equities}

In the equities asset class section, we will discuss stocks and ETFs.
Note that ETFs are grouped under the equities asset class, as their
performance is linked to the price movements of an underlying basket of
stocks.

\subsubsection{Overview of Equity
Markets}\label{overview-of-equity-markets}

This section aims to provide an overview of stock markets. We will first
define some key concepts.

The Dutch East India Company issued the first shares of stock in 1602 in
order to fund its commercial expeditions to Asia, making it the first
publicly listed company. The Amsterdam Stock Exchange, also founded in
1602, was created for trading these shares.

Stock markets, also known as share markets or equity markets, are
defined as platforms where investors engage in the buying and selling of
stocks issued by publicly listed companies, which are listed on stock
exchanges. Stocks, or equity shares, represent ownership claims on a
company's capital (\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran,
2010, pp. 8--10}). They grant shareholders voting rights and entitlement
to dividends, which is the portion of a company's profit that is paid
out. A stock exchange, also known as securities exchange or bourse, is
the regulated trading venue for the buying and selling of shares of
stock. In order to be traded on an exchange, a security must meet
specific listing requirements. Collectively, these exchanges form the
foundation of the global stock market system
(\citeproc{ref-Murugesan2019}{Selvam, 2019}). Regulatory authorities,
such as the U.S. Securities and Exchange Commission (SEC) and the
European Securities and Markets Authority (ESMA), enforce rules and
standards to protect investors.

The stock market is divided into two main segments: the primary market
and secondary market. The primary market is where new stocks are issued
for the first time, usually through an Initial Public Offering (IPO).
The secondary market is where investors buy and sell stocks that have
already been issued, rather than purchasing them directly from the
issuing company.

Stock markets have evolved into a global phenomenon, with both developed
and emerging economies establishing and expanding their own exchanges.
Some of the most active equity markets are found in countries such as
the United States, United Kingdom, Japan, India, China, Canada, Germany,
France, South Korea, and the Netherlands
(\citeproc{ref-Murugesan2019}{Selvam, 2019, p. 14}). According to the
database of the World Federation of Exchanges, the total value of the
global stock market increased over the past few decades (1975 - 2022),
reaching \$104.29 trillion in 2022. A more recent figure provided by the
Securities Industry and Financial Markets Association is \$114.5
trillion in 2023 (\citeproc{ref-SIFMA2024}{Securities Industry and
Financial Markets Association, 2024, p. 5}). This figure highlights the
scale and liquidity of equity markets. Based on their market
capitalization, the largest stock exchanges in the world include the New
York Stock Exchange (NYSE), NASDAQ, Euronext, Tokyo Stock Exchange and
London Stock Exchange. Key equity indexes are the S\&P 500, Dow Jones
Industrial Average, FTSE 100 (UK), CAC 40 (France), Nikkei 225 (Japan)
as well as global indexes such as MSCI World, MSCI Emerging Markets and
MSCI ACWI.

(\citeproc{ref-SIFMA2025}{Securities Industry and Financial Markets
Association, 2025, p. 20}) explains that equity markets aim to make the
allocation of capital efficient by connecting providers of capital with
users of capital (\citeproc{ref-SIFMA2025}{Securities Industry and
Financial Markets Association, 2025, p. 20}). In between, market
intermediaries operate to ensure trade execution, liquidity, and
transparency. For example, brokers execute trades on behalf of clients
and must follow a best execution policy in terms of price, cost, speed,
and likelihood of execution; market makers provide continuous bid and
ask quotations to ensure liquidity and stock exchanges serve as
organized platforms where these transactions take place.

There are about 20 to 25 major stock exchanges, varying in market
capitalization and trading volume. A major stock exchange is defined as
one with a total market cap exceeding \$1 trillion. Based on total
market capitalization of its listed companies, the New York Stock
Exchange (NYSE), founded in 1792, is considered the largest
equities-based exchange (\$25 trillion), representing over 20\% of the
global stock market value.

The Bloomberg United States Exchange Market Capitalization USD tracks
the total market cap of all actively traded securities listed on U.S.
stock exchanges. It represents the aggregate value of outstanding shares
of U.S. listed companies. This index serves as a key indicator of the
size and evolution of the U.S. equity market. It only includes primary
securities on the country's exchanges to avoid double-counting, as
companies can be listed on multiple exchanges. In 2025, for example, the
index reported a total U.S. exchange market cap of over \$58.6 trillion,
making it the largest equity market in the world (according to Bloomberg
Terminal data).

These figures highlight how important the U.S. equity market is within
the global stock market.

We now define some key concepts of equity markets.

A company's market capitalization (``market cap'') is calculated by
multiplying its current share price (market price) by the total number
of shares outstanding and is a measure of corporate size. The market
price is the price at which a security is trading and could presumably
be purchased or sold (\citeproc{ref-Chan2024}{Chan et al., 2024, p.
51}).

The NASDAQ website defines the trading volume as the number of shares
transacted every day. The bid-ask spread is the difference between what
buyers are willing to pay and what sellers are asking for in terms of
price.

The number of shares outstanding (``shares out'') is the total number of
a company's shares that are currently held by all shareholders,
including restricted shares held by insiders, such as company's
executives or strategic investors (\citeproc{ref-Chan2024}{Chan et al.,
2024, p. 51}).

The liquidity of a stock or any financial instrument refers to the ease
with which it can be bought or sold in the market without significantly
affecting its price. High liquidity is associated with a large number of
market participants and a narrow bid-ask spread
(\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, pp. 8--10}).

\subsubsection{Fundamental Valuation
Method}\label{fundamental-valuation-method}

In this section, we aim to provide an overview of how to evaluate the
fair value of a stock, with a focus on the Enterprise Discounted Cash
Flow (DCF) model. We begin by describing the Capital Asset Pricing Model
(CAPM), which is used to estimate the cost of equity. The cost of equity
is a key component of the weighted average cost of capital (WACC) and
the WACC is the appropriate discounting rate in the Enterprise DCF
model, which involves projecting and discounting free cash flows to firm
(FFCF). We also provide an overview of the key input variables used in
computing the cost of equity.

\paragraph{Capital Asset Pricing Model (CAPM) to estimate the cost of
equity}\label{capital-asset-pricing-model-capm-to-estimate-the-cost-of-equity}

In order to price a stock, it is necessary to know its cost of equity.
One method to compute this value is to rely on the capital asset pricing
model (CAPM). Other models include the Fama-French three-factor model
and the arbitrage pricing theory (APT) models. We will rely on the
Capital Asset Pricing Model (CAPM), which is the most common theoretical
way for estimating the cost of equity, by converting a stock's risk into
an expected return.

According to the CAPM, a stock's risk is its sensitivity to the stock
market movements. In contrast, the Fama-French three-factor model
expands this definition by adding two additional sources of systematic
risk: a size factor, which compares the performance of small-cap stocks
with large-cap stocks, and a value factor, which compares stocks with a
high book-to-market ratio (value stocks) to those with a low
book-to-market ratio (growth stocks).

The capital asset pricing model uses three variables to compute a
stock's expected return: the risk-free rate, the market risk premium
(the expected return of the market over the risk-free rate) and the
stock's beta.

The CAPM proposes that the expected rate of return of any security is
equal to the risk-free rate plus the security's beta times the market
risk premium (\citeproc{ref-Koller2010}{Koller et al., 2010, pp.
300--301}):

\begin{equation}\phantomsection\label{eq-capm}{
k_e = r_f + \beta_i \left[ E(R_m) - r_f \right]
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(k_e\): The cost of equity
\item
  \(r_f\): The risk-free rate
\item
  \(\beta_i\): The stock's sensitivity to the market
\item
  \(E(R_m)\): The expected return of the market
\end{itemize}

When pricing a company's stock, a frequent question arises: what
risk-free rate should be applied? Another important consideration is the
estimation of the market risk premium and the company's beta. These
considerations will be addressed in the following sections.

\subparagraph{Estimating the risk-free
rate}\label{estimating-the-risk-free-rate}

The risk-free rate is theoretically defined as the return on an asset,
for example a bond, with zero correlation to market movements (an asset
with a CAPM beta of zero). It is generally recommended to focus on
long-term government default-free bonds in the United States and Western
Europe because, even though they are not necessarily risk-free, they
have at least low betas and, as a result, low correlation with the
market.

Therefore, to estimate the risk-free rate, we refer to government bonds
that are considered default-free. These bonds have various maturities;
for example, the U.S. Treasury issues bonds with maturities between 1
month and 30 years. Since the return varies with the time to maturity,
it is important to determine which maturity we should use.

When pricing a stock and therefore a stream of future expected cash
flows, a particular payment should be discounted using a cost of capital
obtained from a government bond rate of return with maturity similar to
the cash flow being valued. Zero-coupon bonds are preferred because they
provide no interest payments before reaching maturity, which makes their
effective maturity exactly the same as their stated maturity. However,
in practice, for simplicity, market participants discount each cash flow
using a cost of capital derived from the yield to maturity of a
government bond that best matches the period over which the cash flow
stream is estimated (\citeproc{ref-Koller2010}{Koller et al., 2010, pp.
240--242}).

For example, a reasonable proxy for pricing U.S equities is the 10-year
government bond. While longer maturities like the 30-year bond may
better align with long-term cash flows, they are often avoided because
of their lower liquidity, which can cause stale prices and artificially
high yields that may no longer reflect a true risk-free rate. Note that
a stale price is an old price of an asset that does not reflect the most
recent information.

In practice, when valuing European companies, the 10-year German bond
would be preferred because they have higher liquidity and lower credit
risk than bonds of other European countries
(\citeproc{ref-Koller2010}{Koller et al., 2010, p. 241}).

\subparagraph{Estimating the Market Risk
Premium}\label{estimating-the-market-risk-premium}

The market risk premium (MRP) is defined as the difference between the
market's expected return and the risk-free rate
(\citeproc{ref-HarrisMarston2001}{Harris \& Marston, 2001, p. 6}).

A first approach is to use historical data as a proxy for future
estimations, by relying on the average historical spread between stock
returns and bond yields. However, this method requires the analyst to
define a historical period, which can influence the result. Moreover, it
does not incorporate forward-looking expectations
(\citeproc{ref-HarrisMarston2001}{Harris \& Marston, 2001, p. 6}). In
case where no identifiable trend is observed in historical market risk
premium (MRP) data (the MRP has been stable over time), Koller et al.
(\citeproc{ref-Koller2010}{2010, pp. 242--243}) explains that one should
use the longest period possible to reduce the impact of short-term
volatility. Furthemore, analyzing the excess market's return over
10-year government bonds, rather than short-term bonds, is more
appropriate, as this maturity more accurately reflects the time horizon
of a company's cash flows.

Another model would be to base market risk premium (MRP) estimations on
forward-looking expectations. This approach involves substracting the
current long term risk free rate from an implied cost of equity \(k_e\).
According to Koller et al. (\citeproc{ref-Koller2010}{2010, pp.
247--248}), an implied cost of equity \(k_e\) can be estimated using the
Gordon Growth Model (GGM), based on the current dividend yield and
assuming an expected constant dividend growth rate \(g\), as shown
below:

\[
k_e = \frac{D_1}{P_0} + g
\]

Where:

\begin{itemize}
\tightlist
\item
  \(D_1\): The expected dividend next year
\item
  \(P_0\): The current stock price
\end{itemize}

Subtracting the current long-term risk-free rate yields an estimate of
the implied market risk premium (MRP). This implied MRP can then be used
as an input in the Capital Asset Pricing Model (CAPM) to estimate the
cost of equity, as described earlier.

Note that Koller et al. (\citeproc{ref-Koller2010}{2010, pp. 247--248})
proxied the expected dividend in the growing perpetuity formula
(equivalent to the Gordon Growth Model) using the cash flow to equity
holders \(CF_e\), computed as follows:

\[
CF_e = \text{Earnings} \left(1 - \frac{g}{ROE} \right)
\]

Where:

\begin{itemize}
\tightlist
\item
  \(ROE\): The return on equity
\end{itemize}

Note that ``Earnings'' refers to the net income, which is the profit
available to equity holders after all expenses, interest, and taxes have
been paid.

Finally, Harris \& Marston (\citeproc{ref-HarrisMarston2001}{2001, p.
15}) conclude their paper by arguing that there is no single correct
method for estimating the market risk premium (MRP). Instead, the
appropriate approach depends on the prevailing economic context. This
implies that the MRP is not a static figure, but rather a dynamic
concept that varies over time and is sensitive to macroeconomic
conditions.

\paragraph{Estimating Beta}\label{estimating-beta}

According to the CAPM model, the beta aims to measure a stock's
comovement with the market or to capture the systematic risk of an asset
relative to the market. Beta can be greater than 1 (amplifies market
movements), equal to 1 (same volatility as the market), between 0 and 1
(less volatile than the market), or negative (for example, hedging
assets like gold).

To estimate its value, one can use the raw beta which reflects the
stock's historical sensitivity to market movements. In the market model,
a linear regression is used in which the stock's returns are regressed
against the overall market's returns. The slope of the regression line
is known as beta. This is defined by the following equation
(\citeproc{ref-Koller2010}{Koller et al., 2010, p. 312}):

\begin{equation}\phantomsection\label{eq-market-model}{
R_i = \alpha + \beta R_m + \varepsilon
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(R_i\): The return of the individual stock
\item
  \(R_m\): The return of the market index
\item
  \(\alpha\): Shows how much the stock tends to overperform or
  underperform what is predicted by the market
\item
  \(\beta\): The raw beta (slope of the regression line)
\item
  \(\varepsilon\): Captures the firm-specific (news, events) random
  effects on the stock's return that are not explained by market
  movements.
\end{itemize}

The beta coefficient can also be expressed, based on the market model
regression between the stock and the market, as follows:

\begin{equation}\phantomsection\label{eq-beta-covariance}{
\beta = \frac{\text{Cov}(R_i, R_m)}{\text{Var}(R_m)}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(\text{Cov}(R_i, R_m)\): The covariance between the stock and market
  returns
\item
  \(\text{Var}(R_m)\): The variance of the market returns
\end{itemize}

Koller et al. (\citeproc{ref-Koller2010}{2010, pp. 313--316}) recommends
using five years of monthly returns to estimate raw beta, based on the
practice of data providers such as Standard \& Poor's and Value Line, in
contrast to Bloomberg, which uses two years of weekly data for the same
purpose. Furthermore, regarding the market portfolio proxy (as
``{[}\ldots{]} the true market portfolio is unobservable''
{[}p.~316{]}), the author suggests that the S\&P500 for U.S stocks is an
acceptable proxy, while for non U.S stocks, the market typically uses
the MSCI Europe Index or the MSCI World Index.

To improve the cost of equity estimation, using an industry beta can be
also a good alternative instead of relying only on a company specific
beta, based on the idea that companies within the same industry should
face the same operating risks (\citeproc{ref-Koller2010}{Koller et al.,
2010, pp. 317--318}). However, using this alternative does not take into
account the company's financial leverage, as a higher debt-to-equity
ratio increases risk for shareholders and should therefore be reflected
in the beta. As a result, to compute the cost of equity for a company
financed by both equity and debt, we should relever the industry beta to
account for both financial risk and operating risk.

To compute the levered beta, investors can use the Hamada's equation.
This equation has been introduced by Hamada
(\citeproc{ref-Hamada1972}{1972}), a professor of finance at the
University of Chicago Booth School of Business, in the article titled
``The Effect of the Firm's Capital Structure on the Systemic Risk of
Common Stocks,'' published in the Journal of Finance in May 1972
(\citeproc{ref-Hamada1972}{Hamada, 1972}). The equation is:

\begin{equation}\phantomsection\label{eq-hamada}{
\beta_{\text{levered}} = \beta_{\text{unlevered}} \times \left(1 + (1 - T) \cdot \frac{D}{E} \right)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(\beta_{\text{levered}}\): The equity beta (includes both financial
  and business risk)
\item
  \(\beta_{\text{unlevered}}\): The asset or industry beta (only
  business risk)
\item
  \(T\): The marginal corporate income tax rate
\item
  \(D\): Market value of debt
\item
  \(E\): Market value of equity
\item
  \(D/E\): The target debt-to-equity ratio
\end{itemize}

Note that the expression of beta using covariance can also be used to
compute the levered beta, as the observed stock returns used in the
regression already reflect the company's capital structure.

\paragraph{Discounted Cash Flow (DCF)
Methods}\label{discounted-cash-flow-dcf-methods}

To price a share of stock, an investor must estimate the equity value of
the company. In order to do this, there are two main approaches
available: the Enterprise Discounted Cash Flow (DCF) model and the
Equity Cash Flow valuation model. These methods are considered dynamic
approaches, as they take into account not only the current value of a
company's assets but also its future capacity to generate cash flows, in
contrast to static methods (\citeproc{ref-Poncet2022}{Poncet \& Portait,
2022, pp. 266--267}).

In the Enterprise Discounted Cash Flow model, the investor first
estimates the company's operating cash flows and therefore the value of
the company's core operations and then adds the value of any
nonoperating assets to obtain the Enterprise Value. From this, the value
of all nonequity claims (such as debt, unfunded retirement liabilities,
stock options, operating leases, and preferred equity) are subtracted to
obtain the equity value. Finally, this equity value is divided by the
number of shares outstanding to determine the stock price.

In the Equity Cash Flow model, the investor directly estimates and
discounts the expected equity cash flows using the cost of equity,
rather than the weighted average cost of capital (WACC), because only
cash flows available to shareholders are considered.

Both models are based on the principle from Modigliani \& Miller
(\citeproc{ref-Modigliani1958}{1958}), explained in their paper titled
``The Cost of Capital, Corporation Finance and the Theory of
Investment,'' published in The American Economic Review in 1958
(\citeproc{ref-Modigliani1958}{Modigliani \& Miller, 1958}). The authors
argue that the value of a company's economic assets must be equal to the
total value of the financial claims (held by both debt and equity
holders) on those assets. Depending on whether we are computing the
enterprise value or the equity value directly, the relevant cash flows
to consider will differ (\citeproc{ref-Poncet2022}{Poncet \& Portait,
2022, p. 267}).

In this paper, although both methods should yield the same results, we
focus only on the Enterprise DCF model, following the recommendation of
Koller et al. (\citeproc{ref-Koller2010}{2010, p. 105}), as the
implementation of the Equity Cash Flow model is more difficult. For the
interested reader, Koller et al. (\citeproc{ref-Koller2010}{2010, pp.
128--130}) provides an overview of the Equity Cash Flow approach and
explains how to compute equity free cash flows.

\paragraph{Enterprise Discounted Cash Flow (DCF)
Model}\label{enterprise-discounted-cash-flow-dcf-model}

As we explained before, in order to estimate a company's enterprise
value and share price using the Enterprise discounted cash flow model,
an investor must first value the firm's operations. To do this, an
investor must forecast the future free cash flow over a defined explicit
forecast period (during which cash flows can be reasonably projected)
and discount them using the Weighted Average Cost of Capital (WACC).
Since a company that is not in liquidation is expected to continue
growing beyond the explicit forecast period, we do not end the valuation
at that point. Instead, we use a terminal value to capture the remaining
value of the operations, which is also discounted to get its present
value. This will be discussed in more detail later in the paper.

The formula to compute the value of the company's operations is as
follows:

\begin{equation}\phantomsection\label{eq-dcf}{
PVO = \sum_{t=1}^{n} \frac{FCF_t}{(1 + WACC)^t} + \frac{TV_n}{(1 + WACC)^n}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(PVO\): The present value of the firm's operations
\item
  \(FCF_t\): The projected Free Cash Flow in year \(t\)
\item
  \(WACC\): The estimated Weighted Average Cost of Capital (WACC)
\item
  \(n\): The number of years in the explicit forecast period
\item
  \(TV_n\): The Terminal Value at the end of year \(n\)
\end{itemize}

Free Cash Flow (FCF) is the cash flow generated by the company's
operations (independent of leverage), available to all financial
stakeholders (both debt and equity holders) after accounting for capital
expenditures (CAPEX) and variation in net operating working capital. As
a result, free cash flows are discounted using the Weighted Average Cost
of Capital (WACC), as it reflects the risk associated with these cash
flows faced by all investors and therefore represents the required rate
of return.

We believe that the method for projecting free cash flows is beyond the
scope of this paper. Therefore, for the interested reader, Koller et al.
(\citeproc{ref-Koller2010}{2010}) provides an overview of how to project
future free cash flows, especially focusing on the key drivers of these
cash flows, which are the return on Invested capital (ROIC) and growth
(\citeproc{ref-Koller2010}{Koller et al., 2010, pp. 108--112}). His
method involves reorganizing the accounting financial statements to
separate the operating items from non-operating items and financial
structure choices. Note that from these reorganized statements arise two
new metrics: the net operating profit less adjusted taxes (NOPLAT) and
the invested capital. NOPLAT is the after-tax operating profit generated
by the invested capital and available to all investors, whereas invested
capital is the total capital required to fund the company's operations,
regardless of whether it comes from debt or equity
(\citeproc{ref-Koller2010}{Koller et al., 2010, p. 108}).

When the value of operations is computed, the next step is to identify
and add the value of nonoperating assets in order to obtain the
enterprise value. After this, the investor must identify, value, and
subtract all nonequity financial claims on the company's assets (such as
debt, preferred equity, employee options and any unfunded obligations)
from the enterprise value to arrive at the equity value. Finally, to
derive the equity value per share, the equity value is divided by the
number of shares outstanding (\citeproc{ref-Koller2010}{Koller et al.,
2010, p. 106}). An example of nonoperating asset is excess cash and
marketable securities. According to Koller et al.
(\citeproc{ref-Koller2010}{2010, pp. 114--115}), the reported balance
sheet values can be used to value such liquid non-operating assets.

Finally, examples of nonequity claims include debt, preferred stock,
minority interest, employee options, operating leases and unfunded
retirement liabilities. For the interested reader, Koller et al.
(\citeproc{ref-Koller2010}{2010}) provides a detailed explanation of how
to identify and value these items for inclusion in the valuation process
(\citeproc{ref-Koller2010}{Koller et al., 2010, pp. 115--117}).

\paragraph{The cost of capital}\label{the-cost-of-capital}

This section provides a definition of the cost of capital and reviews
the methodology used in its calculation, as it is a key input for
discounting future cash flows to their present value.

To compute the Weighted Average Cost of Capital (WACC), it is necessary
to calculate its three components: the cost of equity, the after-tax
cost of debt and the company's target capital structure.

The WACC represents the average rate of return a company is expected to
pay to all its capital providers (debt and equity) weighted according to
the firm's target capital structure. It is the opportunity cost for
investors choosing to invest in the company instead of others with a
similar level of risk, and is calculated using market values, including
both the cost of equity and the after-tax cost of debt:

\begin{equation}\phantomsection\label{eq-wacc}{
\text{WACC} = \frac{D}{D+E}k_d (1 - T_c) + \frac{E}{D+E}k_e
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(D\): The market value of debt
\item
  \(E\): The market value of equity
\item
  \(k_d\): The cost of debt
\item
  \(k_e\): The cost of equity
\item
  \(T_c\): The company's marginal income tax rate
\end{itemize}

It is important to ensure that both the cash flows and the cost of
capital are expressed in the same currency.

\subparagraph{Estimating Terminal
Value}\label{estimating-terminal-value}

To value a company's operations, we separate its expected cash flows
into two distinct periods, as illustrated below:

\begin{equation}\phantomsection\label{eq-value-decomposition}{
\text{Value} =
\underbrace{\text{Present Value of Cash Flow}}_{\text{during Explicit Forecast Period}}
+
\underbrace{\text{Present Value of Cash Flow}}_{\text{after Explicit Forecast Period}}
}\end{equation}

The second part of the equation is what is known as the Terminal Value
(also referred to as Continuing Value).

Indeed, during the valuation process, after a certain number of years,
it becomes unreliable to predict on a yearly basis the key drivers of a
company's value. Note that these key drivers include the company's
ability to grow its operating profits (NOPLAT) and free cash flows
(FCF), as well as its return on invested capital (ROIC) relative to its
cost of capital (WACC). As a result, to capture the remaining value of
the business's free cash flows beyond the explicit forecast period
without the need for detailed projections, a terminal value is used
based on the assumption that the company will grow forever at a stable
and sustainable rate after that period
(\citeproc{ref-Damodaran2025}{Damodaran, 2025, p. 201}).

No company can grow at a high rate forever and will, at some point,
reach a stable phase of its business life cycle
(\citeproc{ref-Hoover2005}{Hoover, 2005, pp. 337--338}). The reason for
this is that as a company grows, it becomes larger and therefore harder
to sustain high growth rates. Markets tend to become saturated as a
company gains market share, because there are fewer new customers left
to acquire. Over time, new competitors may also enter the market,
potentially reducing the excess returns the company earns above its cost
of capital, depending on the strength of barriers to entry, as well as
the magnitude and sustainability of the company's competitive advantage
(if any). Moreover, to justify a high long-term growth rate, the company
must be able to grow profitably, which requires earning a return on
invested capital (ROIC) that exceeds its cost of capital (WACC)
(\citeproc{ref-Damodaran2025}{Damodaran, 2025, p. 207}; also
\citeproc{ref-Koller2010}{Koller et al., 2010, p. 54}). Regulatory
authorities have also an impact and can limit a company's expansion due
to antitrust concerns, in order to maintain fair competition in the
marketplace.

The terminal value \({V_T}\) at time \(T\) (the final year of the
explicit forecast period) can be calculated using the Gordon Growth
Model:

\begin{equation}\phantomsection\label{eq-gordon-growth}{
V_T = \frac{F_{T+1}}{k - g} = \frac{F_T \times (1 + g)}{k - g}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(F_{T+1}\): The expected free cash flow in the first year after the
  explicit forecast period
\item
  \(F_T\): The free cash flow in the final explicit forecast year
\item
  \(g\): The perpetual growth rate
\item
  \(k\): The cost of capital (WACC)
\end{itemize}

Note that the market practice is to set a value for the long-term growth
rate \(g\) that can be lower than, or at most equal to the growth of the
economy (proxied by the Gross Domestic Product (GDP)) in which the
company operates (if it is a pure domestic firm)
(\citeproc{ref-Damodaran2025}{Damodaran, 2025, p. 203}). Otherwise, the
company would theoretically become an increasingly large part of the
economy before eventually becoming the entire economy, which is
unrealistic (\citeproc{ref-Hoover2005}{Hoover, 2005, p. 338}). If we are
valuing a multinational company that operates in multiple countries, the
growth rate of the global economy should be used as a reference.
Finally, Koller et al. (\citeproc{ref-Koller2010}{2010}) recommends
using the ``long-term rate of consumption growth for the industry's
products'' and adding expected inflation to estimate the terminal growth
rate (\citeproc{ref-Koller2010}{Koller et al., 2010, p. 279}).

\subsubsection{ETFs}\label{etfs}

An Exchange-Traded Fund (ETF) is an investment fund or basket of
securities (such as stocks, bonds, commodities or currencies) that seeks
to replicate or outperform a benchmark index. The first ETF was the SPDR
S\&P 500 ETF (SPY), launched by State Street Global Advisors in 1993 in
the United States to track the S\&P 500 Index.

Exchange-traded funds (ETFs) trade on exchanges, allowing investors to
buy or sell shares throughout the trading day, just like stocks. There
are several types of ETFs, including international ETFs, index ETFs,
stock ETFs, bond or fixed-income ETFs, sector ETFs, and commodity ETFs.
Each of these types provides exposure to a specific asset class, sector,
or geographic region and can be actively or passively managed
(\citeproc{ref-BlackRock2024}{BlackRock, 2024}).

If the fund is actively managed, the goal is to outperform a benchmark
index. If the fund is passively managed, the aim is to replicate the
performance of a benchmark index by taking proportionate exposure to its
components, either through direct investment in all of the component
securities (physical replication) or through the use of derivatives
(synthetic replication), in particular where it may not be possible or
practicable to replicate the index through direct investment
(\citeproc{ref-InvestmentAssociation2018}{The Investment Association,
2018, pp. 6--7}). Note that the synthetic approach allows exposure to
the desired index without holding all of its underlying physical
components, which can be more cost-efficient.

Providing investors with a more efficient way to access financial
markets and express views on specific asset classes, countries and
sectors has contributed to ETF's growing popularity and rising assets
under management since their introduction in the early 1990s. Based on
data published by Intercontinental Exchange on its website in February
2025, global assets under management (AUM) in ETFs reached approximately
\$14.85 trillion in 2024 (\citeproc{ref-ICE2025}{Intercontinental
Exchange, 2025}).

Like stocks, investors can buy and sell ETFs on exchanges throughout the
day using market orders, limit orders, stop-limit orders, and stop-loss
orders, making the trading of ETFs easier and with greater flexibility
and providing investors with more control over both the timing and
execution of their trades. One of the most important advantages of ETFs
compared to mutual funds is their transparency. Indeed, ETFs provide
daily disclosure of their holdings, allowing investors to know exactly
what they own at any given time. Information on ETF holdings,
performance, and costs is published daily on the product page of each
ETF, whereas mutual funds usually disclose their holdings only on a
monthly or quarterly basis (\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \&
Osseiran, 2010, p. 11}).

Before providing an overview of how ETFs are priced, we will define some
key concepts.

The Net Asset Value (NAV) for an ETF is the value of its underlying
assets and cash less its liabilities. It is calculated on a daily basis,
at the end of the trading day using closing prices of the underlying
securities. Dividing the NAV by the number of shares outstanding yields
the fair value of a single share of the fund. Investors can also track
the NAV during the trading day using the Indicative Net Asset Value
(iNAV), which provides real-time estimates of the ETF's NAV
(\citeproc{ref-GlobalX2023}{Global X ETFs, 2023, p. 1}).

The fund sponsor is the issuer and asset manager that creates and
manages the ETF. They are responsible for setting the fund's investment
objective, overseeing the fund's daily operations (including calculating
and publishing daily NAV) and managing the ETF's assets while complying
with the fund's investment objectives. The sponsor works also with
authorized participants (APs) to create and redeem ETF shares.

Authorized Participants (APs) are financial institutions (for example
banks) that have a contractual agreement with the ETF sponsor. Their
main role is to interact with the ETF issuer to create or redeem ETF
shares to help keep the ETF's market price in line with its net asset
value (NAV).

The bid-ask spread of an ETF is the difference between the highest price
buyers are willing to pay (the bid) and the lowest price sellers are
willing to accept (the ask). It reflects the ETF's liquidity and trading
costs. An investor enters an ETF position at the ask price and exits at
the bid price, meaning the bid-ask spread represents a transaction cost.
This notion is important for understanding the pricing deviations
section.

\paragraph{ETFs Pricing}\label{etfs-pricing}

The standard market metric used to price an ETF share is its Net Asset
Value (NAV) per share, which is calculated as follows
(\citeproc{ref-Chan2024}{Chan et al., 2024, p. 84}):

\begin{equation}\phantomsection\label{eq-nav}{
\text{NAV per share} = \frac{\text{Value of the assets held by the fund} - \text{Liabilities}}{\text{Number of Shares Outstanding}}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  Value of the assets held by the fund: The market value of all holdings
  in the ETF
\item
  Liabilities: The total value of all obligations and debts the ETF is
  responsible for
\item
  Number of Shares Outstanding: The total number of ETF shares that have
  been issued
\end{itemize}

Examples of assets include equities, bonds, cash and cash equivalents,
currencies and derivatives (for example futures and total return swaps
used for hedging purposes or to synthetically replicate the performance
of an index and therefore gaining exposure to it in a more cost
efficient way).

Typical liabilities of an ETF include management fees payable (fees owed
to the fund manager, as a percentage of the NAV), custody fees for
safeguarding the assets, administrative expenses (such as legal, audit
fees), operational expenses (including marketing and distribution
costs), short-term borrowings, and payables for securities purchased
(amounts owed for recently acquired securities).

The NAV per share therefore represents the ETF's fair value after
accounting for liabilities. In the next section, we will that, like
stocks, an ETF's market price can deviate from its intrinsic value.

\paragraph{Pricing Deviations: Understanding ETF Premiums and
Discounts}\label{pricing-deviations-understanding-etf-premiums-and-discounts}

In this section, we examine pricing deviations and their underlying
reasons.

At any given time, the price of an ETF reflects the current supply and
demand for the ETF, its net asset value (NAV), and the costs associated
with its underlying securities, such as transaction (bid-ask) spreads
and custody fees (\citeproc{ref-Vanguard2023}{Vanguard, 2023, p. 4} and
7).

Like stocks, ETFs can sometimes trade at prices that deviate from their
fair value due to factors such as investor sentiment, bid-ask spreads in
the underlying securities, execution costs, and market risk (volatility)
(\citeproc{ref-StateStreet2020}{State Street Global Advisors, 2020, p.
3}). When the market price of an ETF is higher than its NAV, it is said
to be trading at a premium and when the market price is lower than the
NAV, the ETF is trading at a discount. A key risk naturally arises when
shares are bought at a significant premium to the NAV and sold later at
a discount.

Note that bid-ask spreads of less liquid underlying securities can
impact ETF prices. They affect the cost for market makers not only to
buy and sell the ETF but also to hedge their exposure to it, since
replicating or offsetting the ETF's position using the underlying assets
becomes more expensive. If market makers need to obtain the individual
securities of an ETF on the secondary market, for example to create or
redeem ETF shares, wider bid-ask spreads increase their trading and
hedging costs. These higher costs are then passed on to the end investor
and reflected in the ETF's price, causing the ETF's market price to
deviate slightly from its NAV. Furthermore, market conditions and
periods of high volatility create uncertainty for market makers about
the prices at which they can hedge their positions. This increased
uncertainty is reflected in wider bid-ask spreads, which compensate for
the higher risk and cost of hedging
(\citeproc{ref-WisdomTree2018}{WisdomTree Europe, 2018, pp. 4--7}).

However, in an efficient market, the market price of an ETF should
remain close to its NAV due to the arbitrage activity of authorized
participants (APs), which occurs through the creation/redemption
process. In the next section, we describe this process and explain how
it helps address pricing deviations.

\paragraph{The Creation and Redemption
Process}\label{the-creation-and-redemption-process}

According to an ETF Education document from the asset management company
State Street Global Advisors, the creation and redemption process takes
place in the primary market, where APs interact with the ETF sponsor to
create or redeem ETF shares (\citeproc{ref-StateStreet2020}{State Street
Global Advisors, 2020, pp. 1--2}). Before that, they operate in the
secondary market to acquire the underlying basket of securities for
creation, or to collect ETF shares for redemption.

When an ETF trades at a premium to its net asset value (NAV), authorized
participants (APs) exploit the arbitrage opportunity and purchase the
underlying securities of the fund and deliver them to the ETF sponsor in
exchange for a creation unit, a large block of ETF shares, usually
consisting of 50,000 shares. These newly created shares are then
introduced into the secondary market, increasing supply and pushing the
ETF's market price back in line with NAV. In the other scenario, when an
ETF trades at a discount, APs can earn a risk-free arbitrage profit by
reversing the process. They collect ETF shares in the secondary market,
assemble them into a redemption unit (again, usually 50,000 shares), and
deliver it to the ETF sponsor in exchange for the underlying individual
securities. These securities can then be sold for a profit, reducing ETF
shares and bringing the market price closer to its NAV
(\citeproc{ref-Parameswaran2022}{Parameswaran, 2022, pp. 460--462};
\citeproc{ref-WisdomTree2018}{WisdomTree Europe, 2018, p. 11}).

As a result, the creation-redemption mechanism enables APs to adjust the
supply of ETF shares in response to investor demand
(\citeproc{ref-Vanguard2023}{Vanguard, 2023, p. 7}). By adding or
removing shares, this process helps maintain the ETF's market price in
close alignment with its NAV and thereby reduce the risk of significant
premiums or discounts.

\subsection{Fixed Income}\label{fixed-income}

\subsubsection{Overview of Fixed Income
Markets}\label{overview-of-fixed-income-markets}

We now turn to the second main asset class examined in this research:
fixed income. Fixed income is the world's largest asset class and refers
to a category of assets and securities that pays a promised, fixed level
of cash flows to investors. It plays an important role in portfolio
construction and diversification by generating stable, predictable
income and reducing overall portfolio volatility. It also helps protect
capital.

Although fixed income traditionally refers to instruments that generate
predictable cash flows, such as bonds, we also include interest rate
derivatives like swaps, inflation swaps, and swaptions. These
instruments are priced using interest rate curves, just like bonds, and
are used to manage interest rate risk or can replicate the cash flows of
a bond.

As of 2024, the global fixed income market was about \$141 trillion in
size, comprising over 3 million unique bonds. This surpassed the global
equity market capitalization, which had a total capitalization of
\$115.0 trillion and included around 9,000 publicly listed stocks
(\citeproc{ref-JPMorgan2023}{J.P. Morgan Asset Management, 2023, p. 5}).
This highlights fixed income as the largest asset class globally in
terms of outstanding value. The global fixed income market mainly
consists of debt securities, such as bonds, which allow corporations,
governments or other entities to raise capital by borrowing from
investors, who in turn provide the necessary funds.

A bond is a debt capital market instrument issued by a borrower or
issuer who agrees to repay the principal amount along with interest
payments to the lender or investor on predetermined future dates
(\citeproc{ref-Choudhry2010}{Choudhry, 2010, p. 3}).

Bond markets are a key segment of the fixed income markets and involve
many participants globally. These include national governments
(sovereign bonds), local and regional authorities (municipal bonds or
``muni'' bonds), supranational institutions (such as the European
Investment Bank or International Monetary Fund) and government-sponsored
entities (such as, in the U.S., Fannie Mae and Freddie Mac). Bonds
issued by corporations, both on domestic and international markets, are
referred to as corporate bonds and their yields are usually higher than
government bonds to compensate investors for the increased default risk.

Each type of issuer uses bonds for different purposes. For example,
governments issue bonds to finance infrastructure projects, defense
spending, and other public services. Corporations use bonds to fund
their day-to-day operations, finance acquisitions, or share buyback
programs. The U.S. Treasury market is considered the most liquid and
efficient fixed income market in the world. Yields on U.S. government
bonds serve as the global benchmark for risk-free interest rates and are
used as a reference for pricing a wide range of financial instruments
across markets.

We now look at some important features of bonds.

The principal of a bond is the amount the issuer commits to repay at
maturity. Also known as the par value, face value, or redemption value,
it represents the base on which interest is calculated. The coupon rate,
or nominal rate, is the annual interest payment the issuer agrees to pay
to the bondholder.

According to an investment strategy report from Morgan Stanley, a bond's
yield, expressed as a percentage rate, is the annual expected return
that an investor can expect to earn, assuming that the bond is held to
maturity and that the bond's issuer does not default
(\citeproc{ref-Edwards2024}{Edwards et al., 2024, p. 2}). The credit
risk refers to the possibility that the issuing entity will default and
be unable to pay back investors' principal and coupons. In this case,
investors do not receive their expected yield.

A bond's term to maturity refers to the number of years remaining until
the issuer repays the principal amount. During this period, the issuer
makes regular interest payments to the bondholder. However, an exception
exists for zero-coupon bonds, which do not make periodic interest
payments and instead repay the full face value at maturity.

A bond's market price is the current price at which it trades in the
market. Investors purchase bonds at the `ask' price and sell them at the
`bid' price. Bonds are typically issued at par value, usually \$1,000
which is repaid at maturity and also serves as the standard trading lot
size in both primary and secondary markets. Prices are quoted as a
percentage of this par value
(\citeproc{ref-FidelityInvestments2017}{Fidelity Investments, 2017}).

\subsubsection{Term Structure}\label{term-structure}

The term structure of interest rates graphically describes the
relationship between term-to-maturity (tenors or also pillars) and
yield-to-maturity for bonds in the same currency, that have the same
level of default risk, differing only in their maturity
(\citeproc{ref-Kolb2006}{Kolb \& Overdahl, 2006, pp. 250--254}). It
helps explain the differences in bond yields that arise only from
differences in maturity.

We now present the key theoretical hypotheses used to explain the
underlying factors that determine the shape of the yield curve.

\paragraph{The Unbiased Expectations
Hypothesis}\label{the-unbiased-expectations-hypothesis}

Originally introduced by Yale economist Irving Fisher (1896), the
expectations hypothesis was later developed by Hicks (1946) and by
Shiller (1990). The theory suggests that the slope of the yield curve
reflects market expectations for future short-term interest rates and
that the long-term interest rate represents the geometric average of
expected future short-term interest rates
(\citeproc{ref-Choudhry2010}{Choudhry, 2010, pp. 50--52}):

\begin{equation}\phantomsection\label{eq-expectations-hypothesis}{
(1 + r_{T})^T = (1 + r_{1})(1 + F_t(1,2))(1 + F_t(2,3)) \cdots (1 + F_t(T-1,T))
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(r_{T}\): Spot yield on a T-year bond
\item
  \(r_{1}\): Today's 1-year spot rate
\item
  \(F_t(T-1,T)\): Forward rate at time \(t\) for the period from \(T-1\)
  to \(T\)
\item
  \(T\): total Investment horizon in years
\end{itemize}

There are four distinct versions of the expectations hypothesis. We
focus in this section only on the unbiased version which states that
current forward rates are unbiased predictors of future spot rates, due
to the existence of arbitrage (\citeproc{ref-Kolb2006}{Kolb \& Overdahl,
2006, pp. 254--259}). The unbiased expectations hypothesis states that
today's forward rate \(F_t(T, T+1)\) at time \(t\) for the period from
\(T\) to \(T + 1\) is the expected value of the one-period spot rate at
time \(T\):

\begin{equation}\phantomsection\label{eq-unbiased-expectations}{
F_t(T, T+1) = \mathbb{E}_t[r_T]
}\end{equation}

The idea is that an investor should earn the same return by for example
investing at today's two-year spot rate or by investing for one year at
the current rate and then reinvesting at the expected one-year rate in
the following year. An upward-sloping yield curve reflects expectations
of rising short-term interest rates, while a downward-sloping curve
suggests that markets anticipate lower rates.

Choudhry (\citeproc{ref-Choudhry2010}{2010}) adds that expectations
about interest rates are also influenced by the outlook for inflation
(\citeproc{ref-Choudhry2010}{Choudhry, 2010, p. 51}). When the market
anticipates rising inflation, yields on longer-term bonds will rise to
compensate for the expected erosion of purchasing power, creating a
positive slope. Conversely, if investors expect inflation to fall, the
curve may slope downwards, as yields on long-term bonds will fall
relative to short-term bonds.

\paragraph{Liquidity Premium
Hypothesis}\label{liquidity-premium-hypothesis}

The Liquidity Premium Hypothesis, described by Hicks (1946), is based on
the idea that borrowers prefer long-term financing, while lenders or
investors prefer and are willing to pay a premium for short-term bonds
because they carry lower interest rate risk and offer greater
flexibility. As a result, long-term bonds must offer a higher return
than short-term bonds to encourage investors to commit capital over
longer periods (\citeproc{ref-Kolb2006}{Kolb \& Overdahl, 2006, pp.
254--259}). The hypothesis argues that forward rates exceed expected
future spot rates by a liquidity premium
(\citeproc{ref-Choudhry2005}{Choudhry, 2005, pp. 63--65}):

\begin{equation}\phantomsection\label{eq-liquidity-premium}{
F_t(T, T+1) = \mathbb{E}_t[r_{T}] + \pi_t(T, T+1)
}\end{equation}

The above equation shows that at time \(t\), the forward rate
\(F_t(T, T+1)\) represents the expected one-period spot rate at time
\(T\) written by \(\mathbb{E}_t[r_{T}]\), plus a liquidity premium. This
premium depends on the bond's term and compensates investors for the
additional risk associated with longer maturities. This hypothesis
rejects the idea that that investors do not have any preferences for
specific maturities. Instead, it argues that forward rates are biased
predictors of future spot rates, due to the existence of a liquidity
premium.

\paragraph{Segmented Markets
Hypothesis}\label{segmented-markets-hypothesis}

The Segmented Markets Hypothesis, first described by Culbertson (1957),
explains that market participants invest in different segments of the
term structure depending on their specific needs and requirements. For
example, banks may prefer short-term bonds to meet their short-term
funding needs, while pension funds invest in longer-term securities to
match their long-term liabilities. Regulatory constraints can also
influence these maturity preferences.

Therefore, the theory suggests that, at any given time, the shape of the
yield curve reflects the actions and preferences of different
participants in the bond market.

A version of this hypothesis is the Preferred Habitat Theory, introduced
by Modigliani and Sutch (1966), which explains that although investors
have preferred maturities, they may invest funds outside their preferred
range if offered attractive yields. Cox, Ingersoll, and Ross (1981)
describe the preferred habitat theory as a form of the liquidity
preference hypothesis, where investors favor short-term bonds for the
reasons explained in the liquidity premium hypothesis section. As a
result, longer-term bonds must offer a premium to attract investors.

\subsubsection{Zero rates and the Zero-coupon yield
curve}\label{zero-rates-and-the-zero-coupon-yield-curve}

This section focuses on zero rates and the zero-coupon yield curve,
which are important tools for pricing financial instruments.

The n-year zero-coupon interest rate, also known as the n-year spot
rate, the n-year zero rate or the n-year zero is the interest rate that
an investor would earn on a bond or swap with no coupon payments
(zero-coupon), which is presented based on a specific day-count
convention and compound frequency. All the interest and principal is
paid at the end of the investment period (\citeproc{ref-Hull2022}{Hull,
2022, p. 104}). Most observable interest rates in the market are not
true zero-coupon rates. For example, the yield on a 10-year risk-free
bond that pays annual coupons does not directly determine the 10-year
zero rate, because part of the bond's return is realized through
periodic coupon payments before maturity.

By definition, a zero-coupon yield curve represents the term structure
of pure interest rates (zero rates). In practice, for clarity,
``zero-coupon yield curve'', ``zero rate curve'' and ``spot rate curve''
are used interchangeably to describe the relationship between
zero-coupon rates and their corresponding maturities. Once the zero rate
for a given tenor is known, the corresponding discount factor can be
derived (\citeproc{ref-Chan2024}{Chan et al., 2024, pp. 24--25}).

A discount factor is a value between 0 and 1 that, when multiplied by a
future cash flow yields its present value
(\citeproc{ref-Choudhry2010}{Choudhry, 2010, pp. 15--17}). It represents
the present value of one dollar earned in the future. Each discount
factor corresponds to a specific maturity for which it has been
calculated. Assuming a continuously compounded zero-coupon rate
\(r(T)\), the corresponding discount factor \(P(0, T)\) is used to bring
a cash flow due at maturity \(T\) back to its present value today. It is
given by (\citeproc{ref-HaganWest2008}{Hagan \& West, 2008, p. 70}) as
follows:

\begin{equation}\phantomsection\label{eq-discount-factor}{
P(0, T) = e^{-r(T) \cdot T}
}\end{equation}

and therefore:

\begin{equation}\phantomsection\label{eq-zero-rate}{
r(T) = -\frac{1}{T} \ln \left( P(0, T) \right)
}\end{equation}

This formula represents the price of a zero-coupon bond under continuous
compounding and we have chosen to use it to price financial instruments
in the practical section.

\paragraph{IBORs, LIBOR, Overnight Rate and
SOFR}\label{ibors-libor-overnight-rate-and-sofr}

In this section, we provide an overview of some key interest rates.

IBORs stands for Interbank Offered Rates and refer to a family of
benchmark interest rates that represent the average rate at which banks
are willing to lend unsecured funds (without collateral or guaranty) to
one another for a given maturity and currency (USD, EUR, GBP, JPY, CHF)
and have been used to establish interest rates for various products,
including derivatives, floating rate notes, loans and mortgages. IBOR
rates included LIBOR (London Interbank Offered Rate), EURIBOR (Euro
Interbank Offered Rate), and TIBOR (Tokyo Interbank Offered Rate). Given
its past global importance, we now turn our attention to LIBOR, which
was the dominant benchmark for interest rates
(\citeproc{ref-DeutscheBank2021}{Deutsche Bank, 2021, pp. 6--18}).

LIBOR means London Interbank Offered Rate, and was the average interest
rate calculated from quotations submitted by creditworthy banks and has
been the world's most widely used benchmark interest rate for pricing
financial transactions worth trillions of dollars
(\citeproc{ref-Hull2022}{Hull, 2022, p. 100}). LIBOR reflected the
unsecured interest rates at which banks were willing to lend to each
other for a number of tenors (1 month, 2 months, 3 months, 6 months, and
12 months) (\citeproc{ref-Parameswaran2022}{Parameswaran, 2022, p.
194}). The 3-month LIBOR was by far the most widely used benchmark.
Contributor banks were originally selected by the British Bankers'
Association (BBA) but since the manipulation scandal in June 2012, the
ICE Benchmark Administration took over LIBOR from the BBA in 2014.

Hull (\citeproc{ref-Hull2022}{2022, p. 99}) describes overnight rates as
the interest rates charged on loans between financial institutions that
are made for one day (overnight). Note that banks are required to
maintain a certain level of reserves with the central bank. At the end
of each day, some institutions may hold excess reserves, while others
may face a shortfall and need to borrow funds to meet their
requirements. In the United States, the overnight rate is known as the
federal funds rate, which is the rate at which banks lend cash to one
another overnight. The effective federal funds rate is the weighted
average of the rates observed in actual interbank transactions. Other
countries have similar overnight rates to the one used in the United
States such as €STR in the Eurozone.

The Secured Overnight Financing Rate (SOFR) is a measure of the cost of
borrowing cash overnight using U.S. Treasury securities as collateral
(\citeproc{ref-ARRC2021}{Alternative Reference Rates Committee, 2021, p.
4}) and is published since April 2018 by the Federal Reserve Bank of New
York on a daily basis. One month later, the 1-month and 3-month SOFR
futures contracts were first launched by CME Group in May 2018. In
October 2020, LCH (London Clearing House) launched clearing of overnight
index swaps based on SOFR. The production of this overnight rate relies
on the volume-weighted median rate of all eligible transactions
(\citeproc{ref-Wu2022}{Wu, 2022, pp. 84--85}, which specifies the
eligible input sources for repo rates). The main difference between
LIBOR and SOFR is that LIBOR was based on estimates submitted by a panel
of global banks, whereas SOFR is based on actual market transactions
data.

To determine the floating-rate payments that must be paid or received
when overnight rates are used as the reference index, instruments such
as SOFR futures and swaps do not rely on a single day's observation of
SOFR but instead are based on an average of daily SOFR observations over
a given accrual period to reflect the actual movements in interest
rates. This average can be calculated using either compound (the market
standard method) or simple interest (less used)
(\citeproc{ref-ARRC2019}{Alternative Reference Rates Committee, 2019,
pp. 3, 19}):

\begin{equation}\phantomsection\label{eq-compound-interest}{
\text{Compound Interest Formula} = \left[ \prod_{i=1}^{d_b} \left( 1 + \frac{r_i \times n_i}{N} \right) - 1 \right] \times \frac{N}{d_c}
}\end{equation}

\begin{equation}\phantomsection\label{eq-simple-interest}{
\text{Simple Interest Formula} = \left[ \sum_{i=1}^{d_b} \left( \frac{r_i \times n_i}{N} \right) \right] \times \frac{N}{d_c}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(d_b\): The number of business days in the interest rate period
\item
  \(d_c\): The number of calendar days in the interest rate period
\item
  \(r_i\): The interest rate on the \(i\)th business day
\item
  \(n_i\): The number of calendar days for which \(r_i\) applies
\item
  \(N\): The number of calendar days in the year, depending on the
  day-count convention used
\end{itemize}

Furthermore, in an OIS swap, the overnight floating rate can be
determined either before the start of the interest period (in advance),
or at the end of the current interest period (in arrears). An average of
SOFR in arrears therefore reflects what actually happened during the
interest rate period, while under an in advance payment structure, the
floating payment is known at the beginning of the accrual period and is
based on a historical average of SOFR observed prior to the start of the
interest rate period (\citeproc{ref-ARRC2019}{Alternative Reference
Rates Committee, 2019, pp. 7, 10}).

\paragraph{Bootstrapping \& Interpolation
Methods}\label{bootstrapping-interpolation-methods}

We now focus on the bootstrapping method, as it is an appropriate
technique for deriving zero-coupon rates from market data.

Indeed, bootstrapping is a method used to construct a zero-coupon yield
curve. The goal of the bootstrapping method is to construct a smooth,
no-arbitrage zero-coupon yield curve. This ensures that the discount
factors obtained for every maturity within the curve range can be used
to exactly reprice the market swap rates used in the curve's
construction. A screenshot taken from the Bloomberg Terminal is provided
below to illustrate the desired output of the bootstrapping process:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/IRS/curve_zero_rates_BBG.png}}

}

\caption{SOFR Zero curve as of 05/23/2025 (Source: Bloomberg Terminal)}

\end{figure}%

The swap curve represents the relationship between term structure and
swap rates accross different maturities. A swap rate, also known as par
swap rate, is the fixed rate that makes the present value of an interest
rate swap contract equal to zero at inception and therefore fair valued.
We will revisit this concept later in the swaps section.

The swap curve is built using observed market quotes from the most
liquid and actively traded fixed income instruments for each maturity.
These instruments should also have a similar level of default risk and
liquidity, so that the swap curve reflects only interest rate
expectations. For this reason, we prefer bootstrapping a swap curve
rather than using a bond curve, which may include distortions due to
differences in credit quality and liquidity
(\citeproc{ref-HaganWest2006}{Hagan \& West, 2006, pp. 93--94}).

Since swap rates are not zero rates, we cannot use directly the swap
curve to extract zero rates and discount factors; it must first be
converted into a zero term structure through a bootstrapping process.
This is important in order to compute pure zero rates and accurate
discount factors.

Basically, the bootstrap is an iterative process. For each tenor, we
assume that the previously bootstrapped discount factors for all earlier
maturities are already known. The only unknown is the discount factor
\(P(0, T_i)\) for maturity \(T_i\), which we solve using the market par
swap rate \(s_i\) for that maturity. This is based on the fact that, at
inception, the present value of the floating leg is equal to the fixed
leg.

The bootstrapping formula provided below illustrates the core idea
behind the bootstrapping method (\citeproc{ref-Bernhart2013}{Bernhart,
2013, p. 3}; and also \citeproc{ref-HaganWest2006}{Hagan \& West, 2006,
p. 92}):

\begin{equation}\phantomsection\label{eq-bootstrapping}{
P(0, T_i) = \frac{1 - s_i \sum_{j=1}^{i-1} (T_j - T_{j-1}) P(0, T_j)}{1 + s_i (T_i - T_{i-1})}, \quad 1 \leq i \leq n
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(D(0, T_i)\): Discount factor at time \(T_i\), to be derived
\item
  \(s_i\): The swap rate corresponding to maturity \(T_i\)
\item
  \(T_j\): The payment date of the \(j\)-th fixed leg cash flow
\item
  \(T_{j-1}\): The payment date immediately preceding \(T_j\) (start of
  the period)
\item
  \((T_j - T_{j-1})\): The year fraction between two payment dates
\item
  \(P(0, T_j)\): The discount factor at time \(T_j\), known from
  previous steps
\item
  \((T_i - T_{i-1})\): The accrual factor for the last period ending at
  \(T_i\)
\item
  \(n\): The total number of fixed payment periods
\end{itemize}

Because market rates are quoted only for specific tenors, we need
interpolation in order to estimate zero rates for maturities that do not
exactly match those tenors. A review of the main interpolation methods
used in financial markets is presented in the article ``Interpolation
Methods for Constructing a Yield Curve'' written by Patrick S. Hagan and
Graeme West and published in 2008 (\citeproc{ref-HaganWest2008}{Hagan \&
West, 2008}). It is worth noting that in the Bloomberg terminal, when
using the function ``SWPM'' (Swap Manager) to build the swap curve and
extract the SOFR market swap rates, the interpolation choices available
include Piecewise Linear (Simple and Continuous), Smooth Forward
(Continuous), and Step Forward (Continuous).

In the conclusion section of their important article ``Interpolation
Methods for Curve Construction'' released in 2006
(\citeproc{ref-HaganWest2006}{Hagan \& West, 2006, p. 127}), the same
authors argue that:

``{[}\ldots{]} the choice of which method to use will always be
subjective, and needs to be decided on a case-by-case basis''.

However, in their 2008 article (\citeproc{ref-HaganWest2008}{Hagan \&
West, 2008, pp. 75--77} and p.~79), they choose the monotone convex
interpolation as their preferred method. Regarding our choice in this
paper, we choose to use the quadratic interpolation method. We briefly
present an overview of this method in the next section.

\paragraph{Quadratic Interpolation
Method}\label{quadratic-interpolation-method}

In this section, we briefly describe the quadratic interpolation method.

In comparison with linear interpolation, which connects two data points
using a straight line (\citeproc{ref-Jobair2021}{Jobair, 2021, pp.
2--3}), the quadratic method introduces curvature to the line by using a
parabola that passes through three known points
\((x_0, y_0), \quad (x_1, y_1), \quad (x_2, y_2)\).

The quadratic interpolation approach aims to find a second-degree
polynomial \(P_2(x)\)(known as Lagrange's form of the quadratic
polynomial) defined by :

\begin{equation}\phantomsection\label{eq-quadratic-interpolation}{
P_2(x) = y_0 L_0(x) + y_1 L_1(x) + y_2 L_2(x)
}\end{equation}

where the Lagrange basis functions \(L_0(x)\), \(L_1(x)\) and \(L_2(x)\)
are given (\citeproc{ref-Vandeboogert2017}{Vandeboogert, 2017, p. 4};
also \citeproc{ref-Jobair2021}{Jobair, 2021, p. 10}), respectively, by:

\begin{equation}\phantomsection\label{eq-lagrange-l0}{
L_0(x) = \frac{(x - x_1)(x - x_2)}{(x_0 - x_1)(x_0 - x_2)}
}\end{equation}

\begin{equation}\phantomsection\label{eq-lagrange-l1}{
L_1(x) = \frac{(x - x_0)(x - x_2)}{(x_1 - x_0)(x_1 - x_2)}
}\end{equation}

\begin{equation}\phantomsection\label{eq-lagrange-l2}{
L_2(x) = \frac{(x - x_0)(x - x_1)}{(x_2 - x_0)(x_2 - x_1)}
}\end{equation}

and these basis functions verify the following properties:

\begin{equation}\phantomsection\label{eq-lagrange-property}{
L_i(x_j) =
\begin{cases}
1, & \text{if } i = j \\
0, & \text{if } i \ne j
\end{cases}
}\end{equation}

for \(i, j = 0, 1, 2\). Because all \(L_i(x)\) are of degree 2, we can
deduce that \(P_2(x)\) is also a polynomial of degree 2. Note that
\(P_2(x)\) is unique.

We consider now \(n+1\) known points:
\((x_0, y_0), (x_1, y_1), \dots, (x_n, y_n)\). The Lagrange's formula
can be extended to \(n+1\) points, defining a unique polynomial of
degree \(n\) that connects all this \(n+1\) points, as follows:

\begin{equation}\phantomsection\label{eq-lagrange-general}{
P_n(x) = y_0 L_0(x) + y_1 L_1(x) + \cdots + y_n L_n(x)
}\end{equation}

and where each Lagrange basis polynomial \(L_i(x)\), with
\(0 \leq i \leq n\), is defined (see \citeproc{ref-Graber2009}{Gräber,
2009, p. 78}; also \citeproc{ref-Jobair2021}{Jobair, 2021, p. 9}) as:

\begin{equation}\phantomsection\label{eq-lagrange-basis}{
L_i(x) = \prod_{\substack{j = 0 \\ j \ne i}}^{n} \frac{x - x_j}{x_i - x_j}
}\end{equation}

Concretely, in the application section, we aim to bootstrap zero rates
from retrieved Bloomberg SOFR swap rates. Given \(n+1\) known zero rates
at points \((T_0, Z_0),\ (T_1, Z_1),\ \dots,\ (T_n, Z_n)\) provided as
output by our python code, we then estimate, for example, the zero rate
at a given tenor \(T\) between two known points using the quadratic
interpolation method.

After presenting the idea behind the quadratic interpolation method and
its application in bootstrapping, we now give a brief overview of the
market's shift toward overnight risk-free rates (RFRs) and OIS
discounting as the new standard. We then describe in more detail the
importance of considering counterparty credit risk in the pricing of
derivatives in order to apply accurate discount factors.

\paragraph{Transition to Overnight Risk-Free Rates (RFRs) and Overnight
Index Swap (OIS)
Discounting}\label{transition-to-overnight-risk-free-rates-rfrs-and-overnight-index-swap-ois-discounting}

This section presents an overview of the context and explains why
traditional interest rates such as LIBOR are no longer used in financial
contracts.

In June 2012, LIBOR manipulation scandal came to light. Instead of
providing the rates at which they were really lending, several banks
were accused of manipulating their LIBOR submissions for their own
profit. Following this, the Alternative Reference Rates Committee
(ARRC), a group of private sector financial institutions was established
in 2014 by the Federal Reserve Board and the Federal Reserve Bank of New
York. The ARRC's mission was to identify an alternative rate to USD
LIBOR based on market transaction data and support the transition to
this alternative overnight risk-free rates (RFRs)
(\citeproc{ref-CME2019}{CME Group, 2019, p. 2}).

The new alternative should have a solid underlying market according to
standards from the Organization of Securities Commissions' (IOSCO)
Principles for Financial Benchmarks. This transition also occurred in
other major currencies. By the end of 2021, the United Kingdom (UK)
adopted the Sterling Overnight Index Average (SONIA), the European Union
selected the Euro Short-Term Rate (€STR), Switzerland adopted the Swiss
Average Rate Overnight (SARON), Japan transitioned to the Tokyo
Overnight Average Rate (TONA), and Canada to the Canadian Overnight Repo
Rate Average (CORRA).

In comparison to LIBOR, SOFR is a secured, overnight interest rate that
reflects the cost of borrowing cash collateralized by U.S. Treasury
securities in the overnight repo market. In the U.S. Treasury repo
market, a repurchase agreement (repo) is an arrangement where one party
sells U.S. Treasury securities (the collateral) with an agreement to
repurchase them at a specified future date and price
(\citeproc{ref-CGFS2017}{Committee on the Global Financial System, 2017,
pp. 4--5}). Unlike LIBOR, SOFR is virtually free of credit risk because
it is based on observed transactions backed by top-quality collateral.

The liquidity of the U.S. dollar Treasury repo market was an important
reason that led in 2017 the ARRC to recommend SOFR as the strongest
alternative for USD LIBOR. Indeed, daily transaction volumes in this
market reached \$1 trillion in 2017 and rose to between \$4 and \$5
trillions by 2023 (in the bilateral repo segment)
(\citeproc{ref-Wu2022}{Wu, 2022, pp. 84--86}). This huge liquidity makes
USD SOFR less likely to be subject to influence and manipulation.
Furthermore, SOFR reflects better risk-free, pure (excluding credit risk
and liquidity risk premium) funding costs in the U.S. financial system
(\citeproc{ref-ARRC2019}{Alternative Reference Rates Committee, 2019, p.
2}).

Before the 2008 Global Financial Crisis (GFC), LIBOR was considered a
good proxy for the risk-free interest rate, and the market standard was
to use a single LIBOR curve for both projecting forward rates (for
floating rate cash-flows) and calculating discount factors (for present
value calculations) (\citeproc{ref-Gregory2020}{Gregory, 2020, pp.
466--468}). The LIBOR curve was constructed using market quotes from
input instruments such as LIBOR deposits (overnight to 3 months),
Eurodollar futures (for maturities up to 3 or 4 years) and par swaps
(for longer-term tenors) (\citeproc{ref-Chan2024}{Chan et al., 2024, pp.
45--48}; \citeproc{ref-ZhouAbramov2019}{Zhou \& Abramov, 2019, p. 7} and
p.11). However, the 2008 GFC, along with a general decline in interbank
lending volumes, highlighted that LIBOR could no longer be considered a
risk-free proxy rate, as it incorporated both credit and liquidity risk.

Using a single LIBOR curve for discounting created pricing distortions,
especially for collateralized and centrally cleared derivatives, where
counterparty credit risk is negligible. As a result, the so-called
dual-curve stripping has been the new market standard practice where a
separate LIBOR curve was used for projecting cash flows and an OIS curve
for discounting these cash flows, in order to better reflect actual
collateralized funding costs. To fully understand the rationale behind
OIS discounting, it is essential to review the role of collateralization
and the legal frameworks that regulate over-the-counter (OTC) and
cleared derivatives.

\paragraph{Building the USD SOFR Swap
Curve}\label{building-the-usd-sofr-swap-curve}

We now turn to the theory behind constructing a swap curve, with a focus
on the USD SOFR swap curve, which is used in our research to bootstrap
zero-coupon rates.

To build a SOFR swap curve, market practice is to use as inputs the most
liquid SOFR derivatives. For the short-end of the curve, overnight SOFR
deposit rates are typically used, followed by 1-Month SOFR futures (for
maturities up to one year), 3-Month SOFR futures (extending to two
years), and finally SOFR OIS swaps with their corresponding swap rates
(the rates that nullify the contract's present value) for maturities up
to ten years (\citeproc{ref-HaganWest2006}{Hagan \& West, 2006, p. 92};
\citeproc{ref-Wu2022}{Wu, 2022, p. 94}).

According to the CME (Chicago Mercantile Exchange) Group, 1-Month SOFR
futures settle to the arithmetic average of the daily SOFR observed over
a given calendar month, whereas 3-Month SOFR futures settle to the
compounded average of daily SOFR over a period between two International
Monetary Market (IMM) dates, and are reliable indicator of SOFR market
expectations (\citeproc{ref-CME2019}{CME Group, 2019, pp. 5--6} and
p.12). For clarity, IMM dates are standardized fixed quarterly
settlement dates (the third Wednesday of March, June, September, and
December) used in futures and swaps markets to improve liquidity. For
example, the IMM dates in 2025 are: March 19, 2025, June 18, 2025,
September 17, 2025 and December 17, 2025.

The Bloomberg terminal uses the following maturities to build the swap
curve: 1 WK, 2 WK, 3 WK, 1 MO, 2 MO, 3 MO, 4 MO, 5 MO, 6 MO, 7 MO, 8 MO,
9 MO, 10 MO, 11 MO, 12 MO, 18MO, 2 YR, 3 YR, 4 YR, 5 YR, 6 YR, 7 YR, 8
YR, 9 YR, 10 YR, 12 YR, 15 YR, 20 YR, 25 YR and 30 YR. Market quotes for
40 YR and 50 YR are also available. We provide below an illustration of
the term structure of the USD SOFR swap curve taken from the Bloomberg
terminal as of 23/05/2025:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/IRS/SOFR_swap_curve.png}}

}

\caption{USD SOFR Swap Curve as of 05/23/2025 (Source: Bloomberg
Terminal)}

\end{figure}%

For maturities beyond ten years and up to fifty years, long-term SOFR
swaps is the main instrument used to construct the curve. Note that in
SOFR swaps, the cash flow structure consists of a fixed leg that pays
interest at a predetermined fixed rate in USD, while the floating leg
pays interest based on the daily compounded SOFR rate. Both legs share
the same notional amount (which is never exchanged) and usually follow
the Actual/360 day count convention (\citeproc{ref-Tuckman2022}{Tuckman
\& Serrat, 2022, pp. 68--70}) .

SOFR Forward Rate Agreements (FRAs) can also be used, but a convexity
adjustment is necessary when using SOFR futures in order to convert the
futures interest rate into a forward rate
(\citeproc{ref-HaganWest2006}{Hagan \& West, 2006, p. 92}). This is
because futures contracts are marked to market daily, whereas FRAs are
settled only at the maturity of the contract and this difference in cash
flow timing creates a ``convexity bias'' (\citeproc{ref-Wu2022}{Wu,
2022, p. 94}). However, in practice, SOFR FRAs are less used than SOFR
futures due to their lower liquidity, as futures are exchange-traded
instruments while FRAs are traded over-the-counter (OTC).

In this paper, convexity adjustments are neglected in order to simplify
the pricing process and we will use the SOFR OIS swap rates as provided
directly by Bloomberg (using the ``SWPM'' - Swap Manager function)
across various maturities. In the application section, we bootstrapped
and interpolated the data using Python in order to obtain the
corresponding zero rates and discount the cash flows of USD-denominated
instruments.

\subsubsection{Counterparty Credit
Risk}\label{sec-counterparty-credit-risk}

This section aims to justify the use of OIS zero rates for discounting
the cash flows of assumed collateralized derivatives, as presented in
the implementation section.

The choice of appropriate discount factors is essential to accurately
replicate market prices. The discount factors used must reflect the risk
profile of the cash flows, which is influenced by counterparty credit
risk (the risk that the other party may default on its contractual
obligations).

To mitigate default risk, counterparties in derivative transactions are
required to post collateral, including initial and variation margins.
The standard market framework governing collateral management and its
remuneration is known as the ISDA Credit Support Annex (CSA), which is
part of an ISDA Master Agreement(\citeproc{ref-Gregory2020}{Gregory,
2020, p. 152}). It is worth noting that some CSAs allow for different
types of collateral posted (cash or securities) and in different
currencies, which leads counterparties to deliver the
``cheapest-to-deliver'' option. Under the CSA, cash collateral is
remunerated at the relevant overnight risk-free rate (in the same
currency as the collateral) such as SOFR for USD denominated
collateralized transactions following the transition away fron LIBOR
(depending on the terms of the CSA
(\citeproc{ref-Schofield2011}{Schofield \& Bowler, 2011, pp. 47--48};
also \citeproc{ref-Gregory2020}{Gregory, 2020, p. 468}). However, as
Gregory (\citeproc{ref-Gregory2020}{2020, pp. 467--468}) explains, OIS
rates are considered acceptable discount rates not only because they are
perceived as risk-free, but because collateral (or margin) poster under
a CSA is remunerated at this rate and also because daily margin calls
make the collateral posted only held overnight (even if in practise, it
may stay for several days or longer), aligning with the overnight
exposure of OIS rates.

As a result, financial institutions adjust discount rates to reflect how
the transaction is financed or collateralised. For derivatives such as
swaps, cash flows are discounted by banks using OIS zero rates. Note
that before the 2008 global financial crisis, LIBOR was used to discount
cash flows, as it was considered by the market to be an acceptable proxy
for the risk-free rate, without considering funding risk (meaning funds
can be borrowed or lent at LIBOR easily, without friction like credit
risk or liquidity constraints) (\citeproc{ref-Gregory2020}{Gregory,
2020, p. 465}). For uncollateralized transactions, especially following
the 2008 crisis, one example of how the market manages counterparty
credit risk is through the Credit Valuation Adjustments (CVA) method.
CVA adjusts the price at which a financial institution enters into a
transaction to reflect the potential default risk of the counterparty,
implied or reflected by its credit default swap (CDS) spread
(\citeproc{ref-Schofield2011}{Schofield \& Bowler, 2011, pp. 46--47}).

\subsubsection{Bond Pricing}\label{bond-pricing}

In this chapter, we will focus on how to price an option-free bond,
meaning a bond without any embedded features.

The price of any financial instrument is based on the present value of
its expected cash flows. Bond traders often use a single discount rate
to value all cash flows (\citeproc{ref-Hull2022}{Hull, 2022, p. 22}).
However, a more accurate method uses the term structure of interest
rates, meaning that each cash flow is discounted using the zero-coupon
rate corresponding to its maturity. Therefore, the theoretical price of
a bond that pays annual coupons, assuming zero rates are measured with
continuous compounding, can be calculated by summing the present values
of all expected cash flows that will be received by the owner of the
bond, including coupon payments and the principal repayment at maturity
(\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, pp. 6--7}):

\begin{equation}\phantomsection\label{eq-bond-pricing}{
P = \sum_{i=1}^{n} C_i \cdot e^{-r(t_i)\cdot t_i}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(P\) = The price of the bond
\item
  \(C_i\) = Cash flow at time \(t_i\)
\item
  \(r(t_i)\) = The zero-coupon rate (continuously compounded) for
  maturity \(t_i\)
\item
  \(t_i\) = Time to maturity of each cash flow
\item
  n = The number of years to maturity and therefore the number of
  interest periods for a bond paying an annual coupon
\end{itemize}

Bonds in the U.S domestic market usually pay coupons on a semiannual
basis. As a result, the bond pricing formula and discounting must be
adjusted to account for two periods per year:

\begin{equation}\phantomsection\label{eq-bond-pricing-semiannual}{
P = \sum_{i=1}^{2n} \left( \frac{C_i}{2} \cdot e^{-r\left(\frac{t_i}{2}\right) \cdot \frac{t_i}{2}} \right)
}\end{equation}

However, both formulas calculate the fair price of a bond on a coupon
payment date, meaning that no accrued interest is included. Indeed, on
the coupon date, accrued interest is zero, so the clean price and dirty
price are identical.

\paragraph{Bond Yield}\label{bond-yield}

A bond yield is the annualized return an investor would earn by holding
a bond until its maturity date, assuming all payments are reinvested at
the same rate. Mathematically, a bond's continuous compounded yield is
the single discount rate that equates the present value of all future
cash flows to its current market price (\citeproc{ref-Hull2022}{Hull,
2022, p. 105}):

\begin{equation}\phantomsection\label{eq-bond-ytm}{
P = \sum_{i=1}^{n} C_i \cdot e^{-y\cdot t_i}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  P: The bond's market price
\item
  \(y\): The bond's yield with continuous compounding
\item
  \(C_i\): The cash flow at tine \(T_i\)
\end{itemize}

According to this formula, a bond's price and yield move in opposite
directions. When the required yield increases, discount rate rise, which
reduce the present value of future cash flows and the bond's price.
Conversely, when yields decline, bond prices increase.

This calculation is equivalent to computing the bond's internal rate of
return (IRR), also known as its yield to maturity (YTM), gross
redemption yield, or yield to workout. Market makers provide for bonds
bid and ask prices but for investors, these prices alone don't indicate
the expected return generated from holding a bond. Therefore bonds with
different issuers, coupon rates, and maturities are compared based on
their yields (\citeproc{ref-Choudhry2005}{Choudhry, 2005, pp. 19--21}).

\paragraph{Par Yield}\label{par-yield}

The par yield for a particular bond is the interest rate that makes the
bond price equal to its par value (\citeproc{ref-Hull2022}{Hull, 2022,
p. 106}). Note that the par value is the same as the principal value,
usually equal to 100. Therefore the interest rate \(y\) that makes a
bind worth par is given by:

\begin{equation}\phantomsection\label{eq-par-yield}{
\sum_{i=1}^{n} C_i \cdot e^{-y\cdot t_i} = 100
}\end{equation}

\paragraph{Dirty, Clean Prices \& Day-Count
Conventions}\label{dirty-clean-prices-day-count-conventions}

The pricing formulas discussed earlier assume that the bond is priced
exactly on a coupon payment date. As a result, they do not take into
account any accrued interest.

A dirty bond price is the price quoted in the market plus the accrued
interest rates. The clean price is the dirty price less the accrued
interest. Bond prices are quoted as clean prices to ensure that price
movements reflect changes in market conditions only, rather than the
passage of time since the last coupon payment
(\citeproc{ref-Fabozzi2021}{Fabozzi, 2021, pp. 74--76}).

Accrued interest reflects the proportion of the next coupon payment that
has been earned since the last coupon payment up to the calculation or
settlement date. When a bond is purchased between coupon dates, the
buyer must compensate the seller for the accrued interest earned during
that period, as the seller will not receive the full coupon at the next
payment. Therefore, investors pay the dirty price when purchasing a
bond.

The settlement date refers to the date on which the ownership of the
bond is officially transferred from the seller to the buyer and when
payment is made.

The day count defines how interest accrues over time between coupon
payments (\citeproc{ref-Hull2022}{Hull, 2022, p. 152}). The interest
accrued from the last coupon payment up to the calculation date is
determined using the following formula
(\citeproc{ref-Choudhry2005}{Choudhry, 2005, pp. 27--28}):

\begin{equation}\phantomsection\label{eq-accrued-interest}{
AI = C \times \left( \frac{N_{\text{f}} - N_{\text{i}}}{\text{BaseYear}} \right)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(AI\): The accrued interest
\item
  \(C\): The coupon rate for the accrual period
\item
  \(N_f\) - \(N_i\): The number of days from the calculation or
  settlement date until the last coupon payment date, based on the day
  count convention used
\item
  \(\text{BaseYear}\): The number of days in a year, according to the
  day count convention (for example 360, 365, or actual number of days)
\end{itemize}

Accrued interest on a bond is calculated by the market using the
day-count convention specified for that bond in its prospectus, such as
actual/actual, 30/360, or actual/360. These conventions define with the
numerator how the number of days between two dates is obtained and with
the denominator, the number of days assumed in a calendar year.

We briefly present the most commonly used day count conventions, as
described by Nawalkha et al. (\citeproc{ref-Nawalkha2005}{2005, p. 47}):

\begin{itemize}
\item
  The actual/actual day count, used for example for U.S. Treasury bonds,
  calculates accrued interest by dividing the real number of days
  between two dates by the actual number of days in a year, then
  multiplying by the appropriate coupon rate.
\item
  The 30/360 day count, used for U.S corporate and municipal bonds,
  assumes 30 days in a month and 360 days in a year.
\item
  The actual/360 method, applied for U.S. Treasury bills and money
  market instruments, uses the actual number of days between dates but
  still assumes a 360-day year as the day-count base.
\item
  The actual/365 convention also exists and is used in the United
  Kingdom (UK) and Canada.
\end{itemize}

\subsection{Derivatives}\label{derivatives}

\subsubsection{Options}\label{options}

This section focuses on the pricing of options on a single stock. Two
option pricing models are examined: the Black-Scholes-Merton (BSM) model
and the Binomial model. In parallel with the pricing of options on
single stocks, we also focus on the Greeks and Newton's method, which is
used to derive implied volatility from market option prices.

\paragraph{Overview of Options
markets}\label{overview-of-options-markets}

In this section, we aim to provide a brief overview of the options
market and define some of its key concepts.

Options markets are marketplaces where investors or speculators can buy
and sell options contracts. These contracts give the buyer (the owner of
the option) the right, but not the obligation, to buy or sell an
underlying asset (such as a stock) at a predetermined price (called the
strike price), on or before a specific date (the expiration date),
depending on the option's exercise style. Note that a European option
can only be exercised at maturity, whereas an American option can be
exercised at any time before the expiry date {[}Yen
(\citeproc{ref-Yen2015}{2015}), p.~18; also p.~22{]}. If the buyer
exercises the option (the option is ``in-the-money'' (ITM)), the
counterparty (the seller or option writer) must deliver or purchase the
underlying asset at the pre-agreed strike price. If the option at
maturity is ``out-of-the-money'', it expires worthless.

Options can be either traded on regulated exchanges or over-the-counter
(OTC). Examples of option exchanges include the Chicago Board Options
Exchange (CBOE, the largest U.S. options exchange), the NASDAQ Options
Market (NOM, where options on U.S. technology stocks are traded) and
EUREX (a major European exchange for options on stocks in Germany).

There are two types of basic options: call options and put options. A
call option gives the holder the right to buy the underlying asset at
the pre-agreed strike price. A put option gives the holder the right to
sell the underlying asset at the strike price
(\citeproc{ref-Liffe2002}{London International Financial Futures and
Options Exchange, 2002, p. 7}). Note that the option buyer must pay a
premium to benefit from this right. The premium is quoted per share of
the underlying asset. Since a standard option contract usually
represents 100 shares of the underlying, the premium per share must be
multiplied by 100 to obtain the total cost of one option contract.
Furthermore, a call option is in the money when, at expiry, the spot
price is above the strike price, and out of the money in the opposite
case. For a put option, it is in the money when the strike price is
above the spot price, and out of the money in the opposite scenario. An
option is said to be ``at-the-money'' (ATM) when the strike price is
equal to the underlying asset's spot price.

The value of an option (or its premium) can be expressed as the sum of
two main components: the intrinsic value and the time value. The
intrinsic value refers to the payoff (or exercise value) one would make
if the option was exercised immediately and is always positive or equal
to zero. Note that the profit made takes the premium paid into account
and is net of it. If the option is out of the money at expiry, its
intrinsic value is zero. The time value, on the other hand, is based on
the remaining time until expiry and reflects the potential for the
option to move in a profitable direction before it expires, due to its
continued exposure to movements in the underlying market price. The
longer the time to maturity, the higher the time value will be, because
the option has more time for the underlying stock price to move in a way
that could make it in the money. Note also that the higher the implied
volatility of the underlying asset, the greater the time value the
market will assign to the option (\citeproc{ref-Liffe2002}{London
International Financial Futures and Options Exchange, 2002, p. 7}).

At maturity \(T\), the payoff (or intrinsic value) of a European call
and put option, respectively, is (\citeproc{ref-Bacha2023}{Bacha, 2023,
p. 323}):

\begin{equation}\phantomsection\label{eq-call-payoff}{
\max[(S_T - K), 0]
}\end{equation}

and

\begin{equation}\phantomsection\label{eq-put-payoff}{
\max[(K - S_T), 0]
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(K\): The strike price
\item
  \(S_T\): The spot price of the underlying stock at maturity \(T\).
\end{itemize}

Options have two main applications in the markets. They can be used for
hedging, meaning to protect investments from potential losses. For
example, if an investor owns a stock and expects its price to drop, they
could buy a put option to limit the losses. In other cases, options are
used for speculative purposes, allowing traders to profit from price
movements in the underlying asset without directly buying or selling the
asset itself.

\paragraph{The Black-Scholes-Merton
Model}\label{the-black-scholes-merton-model}

In the early 1970s, Fischer Black, Myron Scholes, and Robert Merton
introduced a major model for pricing European stock options. Black \&
Scholes (\citeproc{ref-BlackScholes1973}{1973}) published their paper
``The Pricing of Options and Corporate Liabilities'' in the Journal of
Political Economy and Merton (\citeproc{ref-Merton1973}{1973})
contributed by publishing ``Theory of Rational Option Pricing'' in the
Bell Journal of Economics and Management Science''. This marked the
beginning of what is now known as the Black-Scholes-Merton model, also
called the Black-Scholes model, which changed the way options are
priced.

Indeed, the Black-Scholes-Merton (BSM) model demonstrates that the
payoff of an option can be replicated by constructing a risk-free,
dynamically hedged portfolio composed of a risky asset and a risk-free
asset, and by continuously buying and selling the appropriate quantity
of the risky asset (a strategy known as delta hedging)
(\citeproc{ref-Boyle2019}{Boyle \& McDougall, 2019, p. 88}), therefore
avoiding the need to value the underlying asset (in our case a stock),
estimate its future cash flows, or determine an appropriate discount
rate (\citeproc{ref-Bacha2023}{Bacha, 2023, pp. 334--335}). When the
option is written (a short position is taken), the premium received can
be invested in a risk-free asset, such as a bank account or Treasury
bill, to help fund the replication strategy. It requires that the
replicating portfolio holds, at any point in time, a number of units of
the underlying risky asset equal to the option's delta
(\citeproc{ref-Hull2022}{Hull, 2022, pp. 421--422}). Delta measures an
option's sensitivity to changes in the price of the underlying asset. It
varies with both the time to maturity of the option and the spot price
of the underlying asset.

However, before the Black-Scholes-Merton (BSM) breakthrough was achieved
in 1973, option pricing techniques had already been developed. Early
models such as the Bachelier model
(\citeproc{ref-Bachelier1900}{Bachelier, 1900}), the Sprenkle model
(\citeproc{ref-Sprenkle1961}{Sprenkle, 1961}), the Boness model
(\citeproc{ref-Boness1964}{Boness, 1964}), and the Samuelson model
(\citeproc{ref-Samuelson1965}{Samuelson, 1965}) helped lay the
foundation for modern pricing theory (\citeproc{ref-Haug2007}{Haug,
2007, pp. 12--14}). While we will not delve into the details of these
models, our focus will be on the BSM model, which will be implemented in
the practical section using Python.

Before presenting the Black-Scholes pricing formulas, it is helpful to
first review the key assumptions underlying the model. The next section
provides a brief overview of these assumptions.

\paragraph{Underlying Assumptions}\label{underlying-assumptions}

\subparagraph{Log-normal Distribution of Stock
Prices}\label{log-normal-distribution-of-stock-prices}

The Black-Scholes model assumes that the price of the underlying asset
\(S_t\) at time \(t\) follows a geometric Brownian motion :

\begin{equation}\phantomsection\label{eq-geometric-brownian}{
dS_t = \mu S_t\,dt + \sigma S_t\,dW_t
}\end{equation}

where \(\mu\) is the real-world drift (i.e the expected rate of return
on the underlying stock), \(\sigma\) is the volatility of the rate of
return, and \(dW_t\) is a Brownian motion (also called sometimes Wiener
process, representing randomness). \(dS_t\) represents a small change in
\(S_t\) which is driven by known \(dt\) component and a random \(dW_t\)
part.

As a result of this assumption, stock prices are lognormally
distributed, which excludes the possibility of negative stock prices,
while stock returns are normally distributed. Unlike Black, Scholes, and
Merton, Bachelier's model (1900) assumes that asset prices follow a
normal distribution, allowing for the possibility of observing negative
asset prices (\citeproc{ref-Haug2007}{Haug, 2007, pp. 12--13}).

\subparagraph{Constant Volatility}\label{constant-volatility}

The model assumes that the volatility \(\sigma\) of the underlying asset
is known and remains constant throughout the life of the option. In
practice, traders use implied volatilities rather than relying on
historical volatility data. Implied volatility is the volatility implied
by option prices observed in the market and reflect the market's view of
the expected volatility of a particular asset
(\citeproc{ref-Boyle2019}{Boyle \& McDougall, 2019, pp. 89--90}) and is
the correct input (for a given option's maturity and strike) to be
incorporated in the BSM model when trying to replicate observed market
prices (\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, pp.
58--59}).

However, note that in practice, according to Bouzoubaa \& Osseiran
(\citeproc{ref-Bouzoubaa2010}{2010, pp. 58--59}), options with different
strikes and/or maturities have different implied volatilities. This
means that the constant volatility assumption of the
Black-Scholes-Merton model, by its nature, does not account for the
implied volatility skew or the term structure of implied volatilities.
For clarity, for a given strike, the variation of implied volatilities
across maturities is known as the term structure of implied
volatilities. For a fixed maturity, the variation of implied
volatilities across strikes is known as the volatility smile (volatility
skew describes the situation where implied volatility decreases as the
strike price increases, creating a downward-sloping curve rather than a
symmetric smile) (\citeproc{ref-Boyle2019}{Boyle \& McDougall, 2019, pp.
127--128}). The volatility surface is a 3D representation and combines
both the volatility smile (across strikes) and the term structure
(across maturities), illustrating how implied volatility changes in both
dimensions (\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, p.
52}).

We will discuss the Newton's method in a dedicated section, as it is
used to compute the implied volatility of an option based on the option
price obtained with the Black-Scholes-Merton (BSM) model. We also
provide a Python implementation of this method in the results section.

Note that \(\sigma\) is a very important input in pricing an option.
Indeed, as explained by Tan (\citeproc{ref-Tan2010}{2010, pp. 34--36}),
the cost of an option is tied to the cost of dynamically hedging it.
Therefore, higher volatility implies that the hedge must be adjusted
more frequently to maintain a delta neutral position, leading to a
higher cost of replication and, consequently, a higher option premium.

\subparagraph{No Dividends}\label{no-dividends}

The model assumes that the underlying asset pays no dividends during the
option's life. However, the generalized Black-Scholes-Merton option
formula can be extended to price European call and put options on stocks
that pay a known, constant continuous dividend yield equal to \(q\)
(\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, pp. 35--36}).
This is done by incorporating a cost-of-carry rate \(b = r - q\), where
\(r\) is the risk-free interest rate (\citeproc{ref-Haug2007}{Haug,
2007, p. 10}).

Note that the Bloomberg Terminal provides us with the discrete implied
dividend \(d\) for a given option on a dividend paying stock. To convert
a discrete dividend into a continuous dividend yield \(q\), we used the
following formula:

\begin{equation}\phantomsection\label{eq-dividend-yield-conversion}{
q = -\ln\left(1 - \frac{d}{S_0}\right)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(S_0\): the spot price of the underlying stock
\end{itemize}

\subparagraph{Expiration Date}\label{expiration-date}

The options priced are European options, which can only be exercised at
expiration or maturity date. However, the Black-Scholes formula can also
be used to price American call options on stocks that don't pay
dividends, because early exercise doesn't provide any advantage, as the
option's time value would be lost if exercised before expiration
(\citeproc{ref-Yen2015}{Yen, 2015, p. 22}).

\subparagraph{Frictionless Market}\label{frictionless-market}

One of the assumptions of the model is that the market is frictionless,
meaning there are no transaction costs, taxes, or bid-ask spreads and
all participants have equal access to information (no information
asymmetry).

\subparagraph{Constant Risk-Free Interest
Rate}\label{constant-risk-free-interest-rate}

The annualized risk-free interest rate \(r\), assumed to be continuously
compounded, is known and remains constant over the life of the option.
Furthermore, it is also assumed that market participants can borrow and
lend cash at the risk-free rate (\citeproc{ref-Bouzoubaa2010}{Bouzoubaa
\& Osseiran, 2010, pp. 35--36}). It can be proxied, according to
(\citeproc{ref-Boyle2019}{Boyle \& McDougall, 2019, p. 89}), by Treasury
bills, specifically, those with a maturity date close to the option's
expiry.

\subparagraph{No Arbitrage
Opportunities}\label{no-arbitrage-opportunities}

An arbitrage opportunity exists if the following put-call parity,
derived from the generalized Black-Scholes formula for European options
on stocks paying a continuous dividend yield, does not hold
(\citeproc{ref-Haug2007}{Haug, 2007, pp. 9--10}) :

\begin{equation}\phantomsection\label{eq-put-call-parity-call}{
c = p + Se^{(b - r)T} - Xe^{-rT}
}\end{equation}

\begin{equation}\phantomsection\label{eq-put-call-parity-put}{
p = c - Se^{(b - r)T} + Xe^{-rT}
}\end{equation}

where \(b\) is the cost-of-carry of the underlying security.

\begin{itemize}
\tightlist
\item
  \(b = r\) = Cost of carry on a non-dividend-paying stock
\item
  \(b = r - q\) = Cost of carry on a stock that pays a continuous
  dividend yield \(q\)
\end{itemize}

The BSM model operates under the assumption that there are no riskless
profit or arbitrage opportunities available, meaning that put-call
parity holds.

\paragraph{The Generalized Black-Scholes-Merton Option Pricing
Formula}\label{the-generalized-black-scholes-merton-option-pricing-formula}

These formulas, introduced by Black \& Scholes
(\citeproc{ref-BlackScholes1973}{1973}), can be used to price European
options on stocks that pay a continuous dividend yield. We denote by
\(c\) and \(p\) the prices of a European call and put options,
respectively. Below, we present the generalized model, which states
that:

\begin{equation}\phantomsection\label{eq-black-scholes-call}{
c = Se^{(b - r)T} N(d_1) - Xe^{-rT} N(d_2)
}\end{equation}

\begin{equation}\phantomsection\label{eq-black-scholes-put}{
p = Xe^{-rT} N(-d_2) - Se^{(b - r)T} N(-d_1)
}\end{equation}

Where:

\begin{equation}\phantomsection\label{eq-d1}{
d_1 = \frac{\ln(S/X) + \left(b + \frac{\sigma^2}{2}\right) T}{\sigma \sqrt{T}}
}\end{equation}

\begin{equation}\phantomsection\label{eq-d2}{
d_2 = d_1 - \sigma \sqrt{T}
}\end{equation}

And where:

\begin{itemize}
\tightlist
\item
  \(S\): The underlying stock spot price
\item
  \(K\): The strike price of the option
\item
  \(r\): The risk-free interest rate
\item
  \(T\): The time to expiration in years
\item
  \(\sigma\): The volatility of the underlying stock price
\item
  \(N(x)\): The cumulative distribution function
\end{itemize}

The function \(N(x)\) is the cumulative distribution function (CDF) for
a random variable. It yields the probabilities that a standard normally
distributed variable \(\mathcal{X}\) takes on a value less than or equal
to \(x\) and is defined as follows:

\begin{equation}\phantomsection\label{eq-normal-cdf}{
N(x) = \frac{1}{\sqrt{2\pi}} \int_{-\infty}^{x} e^{ -\frac{t^2}{2} } \, dt
}\end{equation}

\paragraph{Greeks}\label{greeks}

In the following section, we provide definitions of the main option
Greeks along with the formulas used to calculate them. An implementation
using Python is provided in the practical section.

\subparagraph{Delta}\label{delta}

As previously discussed, delta represents the rate of change in option
price for a 1\% change in the underlying price. It measures an option's
sensitivity for a very small change in the price of the underlying
asset.

For a call option:

\begin{equation}\phantomsection\label{eq-delta-call}{
\Delta_{call} = \frac{\partial c}{\partial S} = e^{(b - r)T} N(d_1) > 0
}\end{equation}

For a put option:

\begin{equation}\phantomsection\label{eq-delta-put}{
\Delta_{put} = \frac{\partial p}{\partial S} = e^{(b - r)T} [N(d_1) - 1] < 0
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(b\): The cost of carry
\item
  \(r\): The Risk-free interest rate
\item
  \(T\): The time to maturity
\item
  \(N(d_1)\): The cumulative distribution function at \(d_1\)
\end{itemize}

\subparagraph{Gamma}\label{gamma}

Gamma represents the delta's sensitivity to small changes in the price
of the underlying asset and is identical for both call and put options:

\begin{equation}\phantomsection\label{eq-gamma}{
\Gamma_{\text{call, put}} = \frac{\partial^2 c}{\partial S^2} = \frac{\partial^2 p}{\partial S^2} = \frac{n(d_1)e^{(b - r)T}}{S \sigma \sqrt{T}} > 0
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(\sigma\): The volatility of the underlying stock price
\item
  \(n(d_1)\): The standard normal density at \(d_1\)
\end{itemize}

\subparagraph{Vega}\label{vega}

Vega is the sensitivity of an option's price to small changes in the
implied volatility of the underlying asset. It is the same for call and
put options:

\begin{equation}\phantomsection\label{eq-vega}{
\text{Vega}_{\text{call, put}} = \frac{\partial^2 c}{\partial \sigma^2} = \frac{\partial^2 p}{\partial \sigma^2} = Se^{(b - r)T} n(d_1) \sqrt{T} > 0
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(S\): The spot price of the underlying asset
\end{itemize}

\subparagraph{Theta}\label{theta}

Theta measures an option's sensitivity to small changes in time to
maturity. It is the rate at which the option's value declines as time
passes, all else being equal.

For a call option:

\begin{equation}\phantomsection\label{eq-theta-call}{
\Theta_{\text{call}} = -\frac{\partial c}{\partial T} = -\frac{Se^{(b - r)T} n(d_1) \sigma}{2\sqrt{T}} - (b - r)Se^{(b - r)T} N(d_1) - rXe^{-rT} N(d_2) \leq \geq 0
}\end{equation}

For a put option:

\begin{equation}\phantomsection\label{eq-theta-put}{
\Theta_{\text{put}} = -\frac{\partial p}{\partial T} = -\frac{Se^{(b - r)T} n(d_1) \sigma}{2\sqrt{T}} + (b - r)Se^{(b - r)T} N(-d_1) + rXe^{-rT} N(-d_2) \leq \geq 0
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  X: The strike price
\end{itemize}

\paragraph{Binomial Option Pricing
Model}\label{binomial-option-pricing-model}

We now introduce the binomial option pricing model, a numerical method
that allows for the valuation of more complex options, such as American
options, which enable early exercise.

The binomial option pricing method was first introduced by Cox, Ross,
and Rubinstein in their paper ``Option Pricing: A Simplified Approach''
(\citeproc{ref-Cox1979}{Cox et al., 1979}), published in 1979. In the
same year, a similar pricing method was independently presented by
Rendleman and Bartter in their article, ``Two-State Option Pricing''
(\citeproc{ref-Rendleman1979}{Rendleman \& Bartter, 1979}). Both model
are based on a series of discrete time intervals and involves
constructing a binomial tree to represent the various possible paths
that the underlying stock price may take over the life of the option.

Cox, Ross and Rubinstein (CRR) define the up and down factors, such that
at each node in the tree, the spot price \(S\) can either move up by a
factor \(u\) (where \(S_{\text{up}} = S \cdot u\)) or down by a factor
\(d\) (where \(S_{\text{down}} = S \cdot d\)). We will present the
calculation of these factors in the following section, along with the
risk-neutral associated probability \(p\) of an upward movement, and
\(1−p\) for a downward movement, since probabilities must sum to 1.

\subparagraph{\texorpdfstring{Determination of the \(p\), \(u\) and
\(d\)
factors}{Determination of the p, u and d factors}}\label{determination-of-the-p-u-and-d-factors}

In this section, we present the three variables used in a binomial model
with time step (of same length) \(\Delta t\): \(u\), \(d\), and \(p\).
We will assume in this section that the cost of carry is equal to the
risk free rate.

The up and down parameters \(u\) and \(d\) respectively are chosen to
reflect the volatility of the underlying asset and are given by:

\begin{equation}\phantomsection\label{eq-binomial-parameters}{
u = e^{\sigma \sqrt{\Delta t}}, \quad d = e^{-\sigma \sqrt{\Delta t}}
}\end{equation}

where the volatility \(\sigma\) of the underlying stock's return is
annualized. Therefore, over a time step of length \(\Delta t\), the
standard deviation of the stock's return is \(\sigma \sqrt{\Delta t}\).

The corresponding risk-neutral probability \(p\) of an upward movement
(\citeproc{ref-Boyle2019}{Boyle \& McDougall, 2019, p. 74}), assuming no
dividends, is given by:

\begin{equation}\phantomsection\label{eq-risk-neutral-probability}{
p = \frac{e^{r \Delta t} - d}{u - d}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(r\): The risk-free interest rate
\end{itemize}

Because the binomial pricing model operates under the assumption of a
risk-neutral world, the risk-free interest rate \(r\) must be taken into
account (\citeproc{ref-Boyle2019}{Boyle \& McDougall, 2019, pp.
73--75}). In the risk-neutral world described by Hull
(\citeproc{ref-Hull2022}{2022, pp. 270--271}), investors are assumed to
be indifferent to risk. As a result, all assets are expected to earn a
return equal to the risk-free rate \(r\). Therefore, the present value
of an option at a specific node is calculated as the expected future
payoff, discounted at the risk-free rate using the discount factor
\(e^{-r \Delta t}\).

Note that, up to this point, we have assumed that the underlying asset
is a non-dividend-paying stock. If we want to account for dividend
payments, we must adjust the cost of carry \(b\) with the continuous
dividend yield \(q\) (where \(b = r -q\)), and therefore the
risk-neutral probability \(p\), as follows:

\begin{equation}\phantomsection\label{eq-risk-neutral-probability-dividend}{
p = \frac{e^{(r - q) \Delta t} - d}{u - d}
}\end{equation}

This adjustment reflects the fact that part of the return is delivered
through dividend payments, which reduces the probability of stock price
increases. Therefore, the stock price grow more slowly compared to a
non-dividend-paying stock.

\paragraph{Binomial Pricing Formulas for European and American
Options}\label{binomial-pricing-formulas-for-european-and-american-options}

We now define the number of time steps to reach a node as \(j\) (with
\(j \in \{0, 1, \dots, n\}\), and the number of times the underlying
asset price has moved up to reach this node as \(i\) (with
\(i \in \{0, 1, \dots, n\}\)). This create the node \((j,i)\). Note that
the first node is (\(j\) = 0, \(i\) = 0) and we consider \(n\) time
steps, where \(n \in \mathbb{N}^+\). With \(n\) time steps, starting
from 0, the number of nodes in the binomial tree is
\(\frac{(n+1)(n+2)}{2}\) (\citeproc{ref-Haug2007}{Haug, 2007, p. 286}).

We begin by focusing on the case of European options. To price European
call and put options, denoted by \(c\) and \(p\) respectively, which can
only be exercised at the final time step \(n\), the binomial model can
be applied as follows (\citeproc{ref-Haug2007}{Haug, 2007, p. 280}) :

\begin{equation}\phantomsection\label{eq-binomial-european-call}{
c = e^{-rT} \sum_{i=0}^{n} \binom{n}{i} p^i (1 - p)^{n - i} \max(S_0 u^i d^{n-i} - X, 0)
}\end{equation}

\begin{equation}\phantomsection\label{eq-binomial-european-put}{
p = e^{-rT} \sum_{i=0}^{n} \binom{n}{i} p^i (1 - p)^{n - i} \max(X - S_0 u^i d^{n-i}, 0)
}\end{equation}

where the number of possible paths leading to \((j,i\)) is

\begin{equation}\phantomsection\label{eq-binomial-coefficient}{
\binom{j}{i} = \frac{j!}{i! (j - i)!}
}\end{equation}

and the probability of reaching this node is:

\begin{equation}\phantomsection\label{eq-node-probability}{
\binom{j}{i} p^i (1 - p)^{j - i}
}\end{equation}

The pricing formula provided focuses only on the payoffs
max\((S_0 u^i d^{n-i} - X, 0)\) (call option) and
max\((X - S_0 u^i d^{n-i}, 0)\) (put option) at the final time step
\(j\) = \(n\) for each possible end nodes \((n,i)\) and therefore allows
for direct pricing, without the need for backward induction. Note that a
given node \((n,i)\) corresponds to a specific combination of \(i\) up
moves and \(n-i\) down movements in the underlying asset spot price
\(S_0\). Each of these payoffs are then weighted by the risk-neutral
probability \(\binom{j}{i} p^i (1 - p)^{n - i}\) of reaching that node,
and the sum of these weighted payoffs is discounted using the risk-free
interest rate \(r\).

Up to now, we have discussed the binomial model for European options. We
now turn to the case of American options, which can be exercised at any
time before their expiration date. Note that American options will be
discussed again in the next section. For American options, since early
exercise is allowed, we must go backward through the binomial tree
(rolling back through the tree), starting from the option's payoffs at
the end nodes \((n,i)\) and compute the expected value of holding the
option for each prior node \((j,i)\) to compare it with the
corresponding option's immediate exercise value. Marasović et al.
(\citeproc{ref-Branka2014}{2014, p. 1024}) provides the
Cox-Ross-Rubinstein (1979) (CRR) American binomial tree pricing formula,
which is used to compute the expected option value, also called the
``binomial value'', at each node \((j,i)\), assuming the option is held
rather than exercised:

\begin{equation}\phantomsection\label{eq-binomial-value}{
V(j,i) = e^{-r \Delta t} \left( p \, V(j+1, i+1) + (1 - p) \, V(j+1, i) \right)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(V(j, i)\): The option value at node \((j, i)\)
\item
  \(V(j+1, i+1)\): The value at the up node in the next time step
\item
  \(V(j+1, i)\): The value at the down node in the next time step
\item
  \(p\): The risk-neutral probability
\item
  \(r\): The risk-free interest rate
\item
  \(\Delta t\): The length of each time step
\end{itemize}

The binomial value at any given node \((j,i)\) is determined using the
option values for the two next nodes, weighted by the risk-neutral
probabilities \(p\) and \((1-p)\), assuming that the option is not
exercised at the considered node \((j,i)\). However, to determine the
true value of an American option, we must take into account the
possibility of early exercise, as the option can either be held or
exercised at any point before reaching maturity. Therefore, we must
check at each node, as just discussed, by backward induction, if early
exercise is optimal. As a result, the theoretical price at each node is
given below by taking the maximum of the discounted binomial
(continuation) value and the immediate exercise (intrinsic) value:

For a \textbf{call}:

\begin{equation}\phantomsection\label{eq-american-call-value}{
C(j, i) = \max\left(
\text{V(j, i)},\
\max\left(S_{j,i} - X,\ 0\right) = \max\left(S_0 u^i d ^{j-i} - X,\ 0\right)
\right)
}\end{equation}

For a \textbf{put}:

\begin{equation}\phantomsection\label{eq-american-put-value}{
P(j, i) = \max\left(
\text{V(j, i)},\
\max\left(X - S_{j,i},\ 0\right) = \max\left(X - S_0 \cdot u^i \cdot d^{j-i},\ 0\right)
\right)
}\end{equation}

\paragraph{American Options}\label{american-options}

An American option can be exercised at any time before its expiration
date. This additional flexibility makes the premium of an American
option higher than the one from a European option and the pricing
process more complex, because the model must account for a huge number
of possible exercise dates. We previously saw that the BSM model can
still be used to price an American call option on a non-dividend paying
stock, as the optimal strategy is to exercise the option in this case
only at maturity and not before (due to the option's time value).
However, for American put options, early exercise can be optimal,
especially when the option is deep in the money. As a result, the BSM
model is not suitable for pricing American put options. Instead, it is
more appropriate to apply binomial or trinomial tree methods because
they allow at each time step to evaluate whether it is optimal to
continue holding the option or to exercise it immediately.

Because American options cannot be priced exactly as no closed-form
solutions exists (\citeproc{ref-Haug2007}{Haug, 2007, p. 97}), this
section does not focus on exact pricing methods but instead gives
briefly an overview of a single approximation technique: the Bjerksund
\& Stensland (\citeproc{ref-BjerksundStensland2002}{2002})
approximation. We describe now the Bjerksund \& Stensland
(\citeproc{ref-BjerksundStensland2002}{2002}) approximation. The
complete formulas of this model can be found in the book ``The Complete
Guide to Option Pricing Formulas'' published by Haug
(\citeproc{ref-Haug2007}{2007, pp. 104--106}). Note that the book also
presents the Barone-Adesi and Whaley (1987) approximation, which is
another method used for pricing American options on dividend-paying
assets. These methods are also briefly discussed by Boyle \& McDougall
(\citeproc{ref-Boyle2019}{2019, pp. 93--94}).

The Bjerksund \& Stensland (\citeproc{ref-BjerksundStensland2002}{2002})
approximation is a closed-form approximation for pricing American
options. It divides the time to maturity into two separate intervals:
one from the current time \(t_0\) = 0 to an intermediate time \(t_1\),
and another from \(t_1\) to maturity \(T\). These trigger prices \(I_1\)
and \(I_2\) represent specific stock price levels at which it becomes
optimal to exercise the American call option. The boundary \(I_1\)
applies during the first, longer period {[}0,\(t_1\){]} and boudary
\(I_2\) applies during the second, shorter period {[}\(t_1\),\(T\){]}.
Within each interval, trigger prices \(I_1\) and \(I_2\) are assumed to
be constant (flat), with 0 \textless{} \(t_1\) \textless{} T.

Marasović et al. (\citeproc{ref-Branka2014}{2014, p. 1026}) explains
that when \(b \geq r\) (the cost of carry \(b\) is greater than or equal
to the risk-free rate \(r\)), it is not optimal to exercise the option
before expiration because the continuous dividend yield \(q\) is
negligible. In this case, continuing to hold the option until maturity
\(T\) is preferable and therefore the American option can be price using
the BSM model. However, if the option is in the money, meaning
\(S\)\textgreater{}\(I_1\) during the first interval {[}0,\(t_1\){]} or
\(S\)\textgreater{}\(I_2\) during {[}\(t_1\),\(T\){]}, then it is
optimal to exercise the option. Furthermore, if there is an ex-dividend
date before maturity, then receiving the stock and capturing the
dividend may be more valuable than the remaining time value of the
option. However, after the ex-dividend date (assuming no further
dividends), there is no longer any reason to exercise early because the
dividend has been missed and it is better to hold the option until its
maturity.

Finally, Haug (\citeproc{ref-Haug2007}{2007, p. 109}) explains that the
value of an American put can be obtained from an American call using the
Bjerksund \& Stensland (\citeproc{ref-BjerksundStensland1993}{1993})
put-call parity.

\paragraph{Estimating Implied Volatilities using Newton's
Method}\label{estimating-implied-volatilities-using-newtons-method}

We saw that when pricing options using the Black-Scholes model, the
implied volatility is the value that, when input into the pricing model,
gives an option price equal to the market price. However, there is no
closed form solution to obtain the implied volatility of a given option.
As a result, we need to apply a numerical method to estimate the
volatility implied by the option price. We now briefly describe the
Newton-Raphson method.

\subparagraph{Newton-Raphson Method}\label{newton-raphson-method}

The objective of the method is to find the volatility level so that the
Black-Scholes price is equal to the observed market price.

Therefore, we want to find the root of the function \(f(\sigma)\)
defined as the difference between the Black-Scholes price and the
observed market price. This can be written as follows, for example, in
the case of a Black-Scholes call option price \(c\)

\begin{equation}\phantomsection\label{eq-implied-volatility-function}{
f(\sigma) = c(\sigma) - P_{market} = 0
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(c(\sigma)\): The Black-Scholes theoretical call price as a function
  of volatility \(\sigma\), which we want to estimate in order to
  replicate the market price.
\item
  \(P_{market}\): The observed market price of the option
\end{itemize}

\subparagraph{Iterative Formula}\label{iterative-formula}

Newton's method starts with an initial guess \(\sigma_0\). More
generally, for each iteration \(n \in \mathbb{N}_0\), the method updates
the current estimate \(\sigma_n\), using the following formula
(\citeproc{ref-Murray2010}{Murray, 2010, pp. 2--3}) :

\begin{equation}\phantomsection\label{eq-newton-method}{
\sigma_{n+1} = \sigma_n - \frac{f(\sigma_n)}{f'(\sigma_n)}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(\sigma_n\): The current estimate of the implied volatility at
  iteration \(n\)
\item
  \(\sigma_{n+1}\): The updated estimate of the implied volatility
\item
  \(f'(\sigma_n)\): The derivative of the function \(f\) with respect to
  \(\sigma\), evaluated at \(\sigma_n\).
\end{itemize}

In the context of a Black-Scholes call option price \(c\), this can be
written as :

\begin{equation}\phantomsection\label{eq-newton-implied-volatility}{
\sigma_{n+1} = \sigma_n - \frac{c(\sigma_n) - P_{\text{market}}}{\frac{\partial c}{\partial \sigma}(\sigma_n)}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(\frac{\partial c}{\partial \sigma}\): The Vega of the theoretical
  Black-Scholes price
\end{itemize}

Also, a tolerance level \(\varepsilon\) is set, and then the process is
iterated until the difference between two successive estimates is below
this level (\citeproc{ref-Lin2016}{Lin \& Yang, 2016, p. 26}):

\begin{equation}\phantomsection\label{eq-convergence-criterion}{
|\sigma_{n+1} - \sigma_{n}| \leq \varepsilon
}\end{equation}

We understand that Newton's method iteratively refines the initial guess
\(\sigma_0\) until convergence is achieved. At that point,
\(\sigma_{n+1}\) is the final estimate for the implied volatility. Note
that a good initial estimate helps to accelerate convergence, as long as
vega is not close to zero, because otherwise, it can lead to divergence
of the iteration or division by zero errors.

\subsubsection{Interest Rate Swaps}\label{interest-rate-swaps}

\paragraph{Overview of the Swap
Market}\label{overview-of-the-swap-market}

The swap market is one of the largest and most liquid (with small
bid-ask spreads) segments of the interest rate derivatives (IRD) market
and is regulated by the International Swaps and Derivatives Association
(ISDA). The traded notional of interest rate derivatives (IRD) increased
from \$291.9 trillion in 2022 to \$324.5 trillion in 2023. Overnight
Index Swaps (OIS) accounted for 63.1\% of the total IRD traded notional,
while fixed-for-floating interest rate swaps and Forward Rate Agreements
(FRAs) represented 15.2\% and 8.9\%, respectively
(\citeproc{ref-ISDA2024}{International Swaps and Derivatives
Association, 2024, pp. 3--5}).

As over-the-counter (OTC) instruments, swaps are not traded on exchanges
but are instead negotiated directly between counterparties, allowing the
terms to be customized to meet the specific needs of each party. The
relationship between the parties is governed by the ISDA Master
Agreement, which provides the legal framework for OTC derivatives
transactions.

There are several types of swaps, depending on the underlying asset
being exchanged. For example, a currency swap involves the exchange of
currencies; an equity swap refers to the exchange of cash flows based on
the return of an equity index; and in an inflation swap, at least one
party pays cash flows linked to inflation, through a Consumer Price
Index (CPI) (\citeproc{ref-Bacha2023}{Bacha, 2023, p. 433}). The most
common type is the interest rate swap (IRS), which is a contractual
agreement between two counterparties to exchange interest rate cash
flows based on a notional principal amount
(\citeproc{ref-OesterreichischeNationalBank2004}{Oesterreichische
Nationalbank, 2004, p. 16}). The notional principal is most of the time
not exchanged (this can be the case for cross-currency swaps, that we
don't cover in this document), remains constant over the life of the
swap, and is only used for calculating the interest payments.

The most actively traded and liquid interest rate swaps are described as
``plain vanilla''. In a plain vanilla IRS, one party agrees to exchange
a set of fixed interest rate payments (fixed leg) for a set of floating
interest rate payments (floating leg). The fixed rate, which is fixed
for the entire life of the swap, is determined at the initiation of the
contract (based on forward rates extracted from the yield curve), while
the other party pays a floating rate tied to a specified reference term
rate, such as LIBOR or EURIBOR. Both legs have the same payment
frequency.

In comparison, in an overnight index swap (OIS), the floating leg is
based on an overnight rate that resets daily according to market
conditions (like SOFR). Typical interest rate swaps were
fixed-for-floating swaps based on LIBOR
(\citeproc{ref-Tuckman2022}{Tuckman \& Serrat, 2022, p. 319}). However,
since the end of 2021, these instruments are no longer traded for most
currencies, following the transition from LIBOR to alternative reference
rates (RFRs). For USD LIBOR-based swaps, trading continued a bit longer,
officially ending on June 30, 2023
(\citeproc{ref-Richards2022}{Richards, 2022, p. 1}).

By convention, the party paying the fixed rate is said to hold a payer
swap position, while the party receiving the fixed rate is in a receiver
swap. At each payment date, only the net difference between the two cash
flows is exchanged. At the initiation of a swap, no exchange of money is
required upfront because its present value is zero: the fixed rate,
known as the par swap rate, is set so that the present value of the
fixed leg equals the present value of the floating leg and are the
quoted prices in the swap market (\citeproc{ref-Pelsser2000}{Pelsser,
2000, pp. 92--93}). A swap that satisfies this condition is said to be
at par. If this confition is not true, then a payment is made at time
\(t_0\) from one party to the other to offset the initial value
difference.

In the next section, we will focus on the pricing of single currency,
fixed-for-floating swaps.

\paragraph{Pricing Interest Rate Fixed-for-Floating
Swaps}\label{pricing-interest-rate-fixed-for-floating-swaps}

This section explains the methodology for pricing fixed-for-floating
interest rate swaps and computing the present values of both the fixed
and floating legs using zero-coupon discount factors and forward rates.

\paragraph{Calculating the Forward Rate from Spot-Rate Discount
Factors}\label{calculating-the-forward-rate-from-spot-rate-discount-factors}

When pricing a swap, the fixed leg payments are already known and we can
calculate their present values at the start of the swap. However, for
the floating leg, we do not know in advance what the prevailing
reference rates will be. Therefore, we predict them using forward rates
\(F(t_{i-1}, t_i)\) for the payment periods \((t_{i-1}, t_i)\). The
forward rates are implied from current spot prices. The \(i\)-period
linear forward rate can be derived from the prices of two discount
factors maturing at times \(t_i\) and \(t_{i-1}\), using the below
equation (\citeproc{ref-Privault2022}{Privault, 2022, p. 660}; also
\citeproc{ref-Andersen2010}{Andersen \& Piterbarg, 2010, p. 448}) :

\begin{equation}\phantomsection\label{eq-forward-rate}{
F(t_{i-1}, t_i) = \frac{1}{\tau_i} \left( \frac{P(0,t_{i-1})}{P(0,t_i)} - 1 \right)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(F(t_{i-1}, t_i)\): The one-period forward rate applicable for the
  period \(t_{i-1}\) and \(t_i\)
\item
  \(\tau_i\): The day-count fraction between \(t_{i-1}\) and \(t_i\)
  (according to the appropriate day count convention)
\item
  \(P(t_{i-1})\) : The discount factor (zero price) for the period
  \(t_{i-1}\)
\item
  \(P(t_i)\): The discount factor for time \(t_i\)
\end{itemize}

LIBOR forward rates are extracted using spot rates and the corresponding
LIBOR zero-coupon prices (discount factors), which are usually derived
from a LIBOR swap curve. As previously discussed, this curve is
constructed using market quotes from a combination of LIBOR-based
instruments such as money market deposits, interest rate futures, and
swap rates. In comparison, for Overnight Index Swaps (OIS), forward
rates are calculated using discount factors derived from the appropriate
OIS curve.

When a zero-coupon yield curve is derived via a bootstrapping process,
whether from a LIBOR or an OIS swap curve, forward rates can then be
computed using the previously mentioned equation, allowing for the
estimation of floating leg payments of an interest rate swap. The same
zero-coupon yield curve can also be used to calculate the present values
of both the floating and fixed legs of an interest rate swap. Each cash
flow on both legs is discounted using the appropriate discount factor
for its maturity.

\paragraph{Pricing Fixed-for-Floating Interest Rate
Swaps}\label{pricing-fixed-for-floating-interest-rate-swaps}

In this section, we give an overview of how fixed-for-floating interest
rate swaps are priced.

Chan et al. (\citeproc{ref-Chan2024}{2024}) provides a pricing formula
to compute the present value of a receiver interest rate swap, where the
value of the swap is the present value of the fixed leg minus the
present value of the floating leg (\citeproc{ref-Chan2024}{Chan et al.,
2024, p. 40}):

\begin{equation}\phantomsection\label{eq-swap-pv}{
\text{Swap PV} = PV_{\text{fixed}} - PV_{\text{floating}} = s \sum_{j=1}^{m} P(0,t_j) \tau_j - \sum_{i=1}^{n} P(0,t_i) F(t_{i-1}, t_i) \tau_i
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(m\) : Number of remaining cash flows for the fixed leg
\item
  \(n\) : Number of remaining cash flows for the floating leg
\item
  \(s\) : Swap rate of the fixed leg
\item
  \(\tau_i\) : Day count fraction for the \(i\)-th period
  \((t_{i-1}, t_i)\) , depending on the day count used
\item
  \(P(0,t)\) : The today price of the zero-coupon bond with maturity
  \(t\) or discount factor
\item
  \(F(t_{i-1}, t_i)\) : The market-implied forward rate for the \(i\)-th
  period \((t_{i-1}, t_i)\)
\end{itemize}

We observe that the fixed-rate payer in a plain vanilla interest rate
swap holds a long position in a floating-rate bond and a short position
in a fixed-rate bond, both with the same face value. Conversely, the
floating-rate payer holds a long position in a fixed-rate bond and a
short position in a floating-rate bond, also with the same face value.
We note that different indices \(i\) and \(j\) are used to reflect the
fact that the payment frequency of the fixed and floating legs may
differ. However, if both legs have the same frequency, then a single
index can be used (where \(i\) = \(j\)).

In essence, a swap is a series of cash flows, and therefore its value is
determined by discounting all those cash flows to the present (valuation
date). The cash flows are discounted using spot rates and the sum of
these present values is the value of the swap. A question that arises
is: which zero-coupon rates to use ? As noted in
Section~\ref{sec-counterparty-credit-risk}, it is important to remember
that the discount factors used should reflect both the risk profile of
the cash flows and the rate at which the collateral is remunerated.
Therefore, to discount collateralized cash flows, assuming no
counterparty default risk, we use OIS discount factors. The
corresponding zero rates are extracted from a risk-free zero-coupon
curve, bootstrapped and interpolated from the relevant OIS curve,
specifically, the SOFR swap curve for USD denominated, collateralized
derivatives. For LIBOR swap payments, we saw that market practice has
shifted to using OIS discounting since the 2008 global financial crisis
(GFC).

Furthermore, forward rates \(F(t_{i-1}, t_i)\) for the periods
\((t_{i-1}, t_i)\), where \(( i = 1, 2, \dots, n)\) are required as
inputs. These rates can be derived, as discussed in the previous
section, from the prices of two zero-coupon bonds (discount factors)
maturing at times \(t_{i-1}\) and \(t_i\). According to the expectation
hypothesis and the no-arbitrage principle, an investor should have no
preference between investing from today \(t_0\) until \(t_i\), or
investing until \(t_{i-1}\) and then reinvesting from \(t_{i-1}\) to
\(t_i\). The linear forward rate is given by:

\begin{equation}\phantomsection\label{eq-forward-rate-swap}{
F(t_{i-1}, t_i) = \frac{1}{\tau_i} \left( \frac{P(0,t_{i-1})}{P(0,t_i)} - 1 \right)
}\end{equation}

We substitute this expression into the floating leg pricing formula,
which gives (based on \citeproc{ref-Chan2024}{Chan et al., 2024, p. 42};
also \citeproc{ref-Wu2022}{Wu, 2022, pp. 91--94}) :

\begin{equation}\phantomsection\label{eq-floating-leg-pv}{
\text{PV}^{\text{floating}} = \sum_{i=1}^n P(t_i) F(t_{i-1}, t_i) \, \tau_i
= [P(0,t_0) - P(0,t_1)] +  \cdots + [P(0,t_{n-1}) - P(0,t_n)] = P(0,t_0) - P(0,t_n)
}\end{equation}

and therefore:

\begin{equation}\phantomsection\label{eq-receiver-swap-pv}{
\text{Swap PV}_{\text{receiver}} = \text{PV}_{\text{fixed}} - \text{PV}_{\text{floating}}
= s \sum_{j=1}^{m} P(0,t_j) \, \tau_j - \left[ P(0,t_0) - P(0,t_n) \right]
}\end{equation}

As we discussed in the swap market overview section, the swap present
value is usually zero at initation, making
\(\text{PV}_{\text{floating}}\) = \(\text{PV}_{\text{fixed}}\), which
gives:

\begin{equation}\phantomsection\label{eq-swap-rate}{
S(0) = \frac{P(0,t_0) - P(0,t_n)}{A(0)}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(A(0) = \sum_{j=1}^{n} P(0, t_j), \tau_j\) is the annuity factor at
  the contract's initiation
\end{itemize}

Note that in fixed-for-floating swaps based on term rates such as LIBOR
or EURIBOR, the fixing of the floating rate is done in advance (forward
looking), at the beginning of each period, while payments are made in
arrears. In contrast, OIS swaps rely on realized overnight (backward
looking), usually compounded daily over the relevant period, with both
the rate fixing and the payment occurring in arrears. The fixed leg
typically pays on a annual or semiannual basis, while the floating leg's
payment frequency generally aligns with the tenor of the reference rate
(\citeproc{ref-Tuckman2022}{Tuckman \& Serrat, 2022, pp. 323--325}).

\paragraph{Applications}\label{applications}

In this section, we provide a brief overview of the main applications of
interest rate swaps (IRS).

Interest rate swaps (IRS) can be used by investors to transform the cash
flow pattern of an asset or a liability in order to hedge their exposure
to variable or fixed interest rates
(\citeproc{ref-Choudhry2005}{Choudhry, 2005, pp. 125--127}).

For example, on the liability side, interest rate swaps (IRS) allow
market participants to manage and reduce borrowing costs. An investor or
company can convert a floating rate loan into a fixed rate borrowing in
order to hedge its exposure to a rise in interest rates
(\citeproc{ref-Parameswaran2022}{Parameswaran, 2022, p. 422}). In
another case, if a company or government has borrowed at a floating rate
and interest rates have decreased, it can take advantage of this drop by
entering into a payer IRS, which helps reduce its overall borrowing
cost.

Furthermore, an IRS can transform an asset, for example, earning a
floating rate into one earning a fixed rate to lock in stable returns,
or convert a fixed-rate asset into a floating-rate one with the help of
a receiver swap, in order to benefit from higher income if interest
rates are expected to rise (\citeproc{ref-Hull2022}{Hull, 2022, pp.
154--156}).

It is also worth noting that this approach can also be used to hedge
interest rate exposure to bond instruments. To hedge a long exposure to
a bond, an investor or government would need to take a long position in
a payer IRS, meaning paying fixed and receiving floating. In the other
case, to hedge a short bond position, the investor or government would
take a short position in a payer swap, as a result receiving fixed and
paying floating (\citeproc{ref-Choudhry2005}{Choudhry, 2005, p. 130}).

For banks, transforming the nature of their assets and liabilities can
help manage and hedge interest rate mismatches, reducing therefore their
exposure to interest rate risk. For example, if a bank remunerates
deposits at a fixed rate but receives income from mortgages or loans at
floating interest rates, a decrease in interest rates can lead to a
mismatch between assets and liabilities. In this case, the bank
continues to pay the same fixed amount to depositors while receiving
less income. To hedge this risk, the bank can, for example, convert its
floating rate income into fixed income by taking a long position in a
receiver interest rate swap (IRS). Being long a receiver IRS means
paying floating (that is received from depositors) and receiving
fixed(\citeproc{ref-Bacha2023}{Bacha, 2023, pp. 440--441}).

Bacha (\citeproc{ref-Bacha2023}{2023}) also explains
(\citeproc{ref-Bacha2023}{Bacha, 2023, p. 441}) that interest rate swaps
(IRS) can be used for speculative purposes, meaning that participants do
not have an existing exposure to interest rates but instead enter into
an IRS to bet on the direction of future interest rate movements.
Therefore, if a speculator expects interest rates to rise, they can
enter into a payer IRS, receiving floating and paying fixed, in order to
benefit from the increase in floating rates. If a speculator believes
that interest rates are expected to fall, it would be more appropriate
to enter into a receiver IRS, receiving fixed and paying floating.

\subsubsection{European Swaptions}\label{european-swaptions}

We have chosen to include swaptions within the scope of this research
because they are a hybrid product combining instruments previously
discussed, such as interest rate swaps and options.

A swaption, or swap option, is an option on a swap that provides the
right, but not the obligation, to enter into an interest rate swap at a
specified future date, at a predetermined swap rate and for a specific
tenor (\citeproc{ref-Corb2012}{Corb, 2012, pp. 166--167}). The fixed
rate of the underlying swap is the strike price of the option. In this
section, Before discussing how swaptions are priced using Black's
formula and their main applications in the market, we first provide a
brief introduction of the product.

The x X y-swaption has an expiry in x years and an underlying swap
length/tenor of y years. For example, a 1Y × 5Y swaption refers to an
option that expires in 1 year and grants the right to enter into a 5
year interest rate swap (IRS). Like for IRS, swaptions can be
distinguished based on which party pays the fixed rate. A payer swaption
gives the holder the right to enter into a swap as the fixed-rate payer
(and receive floating) and is therefore a call swaption. A receiver
swaption gives the right to enter as the fixed-rate receiver (and pay
floating). In this case, when the buyer of the option is the floating
rate payer, it is a put swaption. Note that swaption prices are not
quoted as option premiums, but rather in terms of implied volatilities.

Swaptions are, like plain vanilla options, characterized by their
exercise style, which can be European (exercise only at maturity) or
Bermudan (exercisable on specific dates prior to expiry). We focus in
the next section on pricing only European style swaption. Furthermore,
if the option holder decides to exercise their right (the swaption is in
the money at expiry), the position can be settled either physically, by
entering into the underlying swap or cash settled, in which case the
holder receives the net present value (NPV) of the underlying swap
observed at the maturity of the option
(\citeproc{ref-Andersen2010}{Andersen \& Piterbarg, 2010, pp.
205--206}).

\paragraph{The Black Model for
Swaptions}\label{the-black-model-for-swaptions}

Fabozzi (\citeproc{ref-Fabozzi2021}{2021, pp. 1606--1611}) uses the
lattice approach to price swaptions. However, in the market, European
swaptions are usually priced using the Black model
(\citeproc{ref-Choudhry2005}{Choudhry, 2005, p. 122}; also
\citeproc{ref-Dash2021}{Dash, 2021, p. 154}). We focus only, as a
result, in the next section, on giving an overview of how to price
European swaptions using the Black model, which assumes that the forward
breakeven rate follows a lognormal distribution (i.e, the implied
volatility used in the Black model is lognormal)
(\citeproc{ref-Dash2021}{Dash, 2021, p. 154}).

\subparagraph{Payoff and Valuation}\label{payoff-and-valuation}

A \textbf{payer swaption} is a call option on the swap rate. The holder
has the right to enter a swap where they \emph{pay} a fixed swap rate
set at expiry and receives a floating leg if the swap rate at expiry is
bigger than the swap rate / strike \(X\). This option is valuable if the
market swap rate \(R\) at expiry is higher than \(X\).

A \textbf{receiver swaption} is a put option on the swap rate. The
holder has the right to enter a swap where they \emph{receive} a fixed
swap rate set at expiry and pay a floating leg if the swap rate is lower
than the swap rate / strike \(X\). This option is valuable if the market
swap rate \(R\) at expiry is lower than \(X\).

\subparagraph{The Pricing Formulas}\label{the-pricing-formulas}

To value the swaption, we first need to calculate the present value of a
basis point (PVBP), also known as the annuity factor. This represents
the present value of all fixed-leg cash flows of the underlying swap if
the fixed rate were 1\% (or 0.01). It's what translates the value of an
option on a rate into a monetary value.

The annuity factor at inception, \(A(0)\), is calculated as:
\begin{equation}\phantomsection\label{eq-swaption-annuity}{
A(0) = \sum_{i=1}^{n} \tau_i\,P(0, t_i)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(n\): Total number of fixed-rate payments in the underlying swap.
\item
  \(\tau_i\): The day-count fraction for the \(i\)-th payment period
  (e.g., approximately 0.5 for semi-annual payments).
\item
  \(P(0, t_i)\): The discount factor from each payment date \(t_i\) back
  to the valuation date (time 0).
\end{itemize}

The present value of a payer swaption is given by
(\citeproc{ref-Corb2012}{Corb, 2012, p. 174}):

\begin{equation}\phantomsection\label{eq-payer-swaption}{
PV_{\text{payer}} = N\,A(0)\,\left[ S(0) N(d_1) - X N(d_2) \right]
}\end{equation}

The present value of a receiver swaption is given by
(\citeproc{ref-Corb2012}{Corb, 2012, p. 174}):
\begin{equation}\phantomsection\label{eq-receiver-swaption}{
PV_{\text{receiver}} = N\,A(0)\,\left[ X N(-d_2) - S(0) N(-d_1) \right]
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(N\): The notional amount of the swap
\item
  \(A(0)\): The annuity factor (PVBP) as defined above
\item
  \(S(0)\): The forward swap rate determined at the valuation date for
  the underlying swap
\item
  \(X\): The strike rate of the swaption
\item
  \(N(\cdot)\): The cumulative standard normal distribution function
\end{itemize}

The terms \(d_1\) and \(d_2\) are calculated as follows
(\citeproc{ref-Corb2012}{Corb, 2012, pp. 173--174}) :

\begin{equation}\phantomsection\label{eq-swaption-d1}{
d_1 = \frac{\ln\left(\frac{S(0)}{X}\right) + \frac{1}{2} \sigma^2 T}{\sigma \sqrt{T}}
}\end{equation}

\begin{equation}\phantomsection\label{eq-swaption-d2}{
d_2 = d_1 - \sigma \sqrt{T}
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(T\): The time to the swaption's expiry in years
\item
  \(\sigma\): The implied volatility of the forward swap rate \(F_0\)
\end{itemize}

\paragraph{Applications}\label{applications-1}

There are several purposes for using European swaptions.

Like for IRS, swaptions can be used by institutions for hedging
purposes, in order to protect against adverse movements in interest
rates that can have an impact on their assets or liabilities.

For example, a mortgage portfolio manager manages mortgages that carry
pre-payment risk when interest rates fall as clients want to refinance
or reimburse earlier, which is disadvantageous for the portfolio manager
as he receives cash flows earlier than expected and he must reinvest at
lower rates. The manager is worried about a fall in the next year in the
10-year swap rates, which would signal a falling interest rate
environment and increase the likelihood of mortgage prepayments.
Therefore to hedge against this risk, the manager decides to enter into
a 1Y X 10Y swaption, which gives him the right but not the obligation to
receive fixed and pay floating in one year. This can be considered as a
hedge as if interest rates fall below the swaption strike rate (fixed
rate), the receiver swaption position becomes profitable as the
portfolio manager will receive a higher fixed rate than the prevailing
market rate, which can help offset the loss from reinvesting at lower
market rates (\citeproc{ref-Corb2012}{Corb, 2012, p. 170}).

Another typical example using swaptions for managing interest rate risk
is the following. A corporate treasurer manages a company that has a
\$200 million floating rate loan tied to 6 month LIBOR + 150 bps over 4
years. The company is concerned regarding a potential increase in
interest rates in the near term which could impact its borrowing costs.
To hedge this risk, the treasurer decides to enter into a 6M × 4Y payer
swaption with a strike rate of 4\%. This swaption gives the company the
right to pay fixed and receive floating in 6 months if the option is
exercised at expiry. If, in six months, interest rates rise above 4\%,
the company will exercise the swaption (physically settled) and
therefore enter into the underlying swap, locking in the 4\% fixed rate
and protecting itself from interest rate increases. If in 6 months,
market rates on the contrary fall below 4\%, then the option expires
worthless and the company benefits from lower prevailing rates on its
existing loan.

Swaptions can also be used by market participants to speculate on the
future directions of interest rates but also to take positions on
implied volatility, whether for speculative or risk management purposes
(\citeproc{ref-Corb2012}{Corb, 2012, p. 170}).

\subsubsection{Inflation Swaps}\label{inflation-swaps}

In this section, we begin by defining inflation and introducing the main
inflation indices used in the inflation derivatives market. We then
define what is a inflation swaps, provide an overview of their pricing
methodology, and review their key applications.

Inflation is the rate of increase in the price of goods and services
over a period of time, eroding purchasing power. Because many investors
seek to protect their returns against this loss in purchasing power and
transfer the risk of inflation, there is strong demand for inflation
derivatives. Inflation is tracked through Consumer Price Indices (CPIs).
A consumer price index is a statistical estimate of the change in prices
of a representative basket of goods and services purchased by households
(\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, p. 289}).
Inflation is usually measured as the percentage change in the Consumer
Price Index (CPI) compared to the same month one year earlier. These
indexes are used to highlight periods of rising or falling inflation and
are therefore watched closely by central banks, like the European
Central Bank (ECB) and Federal Reserve (FED), to guide monetary policy
decisions. Significant increases in a CPI over a short period of time
might indicate a period of inflation.

We now describe the main indices used in inflation swap markets, as
described by Ramirez (\citeproc{ref-Ramirez2015}{2015, pp. 711--712}).

In order to price an inflation swaps, a reference measure (inflation
index) is required. For the U.S. inflation derivatives market, the All
Items Consumer Price Index for All Urban Consumers (CPI-U), published by
the U.S. Bureau of Labor Statistics, is the reference index and is
available both in its seasonally adjusted and unadjusted formats.
However, because the underlying basket of goods and services it tracks
is changed every two years, the FED prefers to also pay attention to the
Personal Consumption Expenditures Price Index (PCE) as the PCE adjusts
every month to reflect changing consumer behavior. In the Eurozone
inflation derivatives market, which is the most liquid and active
inflation market, the reference benchmark is the Harmonized Index
Consumer Prices (HICP), published on a monthly basis by Eurostat
(usually two weeks after the end of the month). The HICPxT excludes
tobacco. In France, it is the French Consumer Price Index, also released
each month, by INSEE. Finally, the benchmark for the UK inflation market
is the Retail Price Index (RPI), published by the Office for National
Statistics. It is important to note that these indexes may be subject to
revision if the statistical agency believes the initial publication was
inaccurate. However, inflation swaps are priced using the unrevised
version of the index.

After presenting the main inflation indexes, we now define what an
inflation swap is. It is a contractual agreement between two
counterparties to exchange inflation-linked cashflows. One party pays a
fixed rate and receives from the other party a floating rate tied to an
inflation index (a consumer price index or CPI). In contrast with IRS,
the inflation type denomination is not based on the fixed rate but
instead on the floating rate. An inflation receiver receives the
floating payments indexed to inflation and pays the fixed rate, whereas
an inflation payer pays the inflation and receives the fixed rate.

Inflation swaps are the most common inflation derivatives and among
them, there are two main types we will focus on in this paper:
zero-coupon inflation swaps (ZCIS) and year-on-year (YoY) inflation
swaps. Both types are quoted in the market in terms of the corresponding
fixed rate \(X\) that makes the swap have zero market value at
inception. Zero-coupon inflation swaps are the most commonly traded
instruments in the inflation markets of the Euro area, the United States
(U.S.), and the United Kingdom (UK) (\citeproc{ref-James2023}{James \&
Webber, 2023, p. 35}). Their pricing methodology is discussed in the
following section.

\paragraph{Zero-Coupon Inflation Swaps
(ZCIS)}\label{zero-coupon-inflation-swaps-zcis}

A zero-coupon inflation swap is a contract in which one counterparty
pays a compounded fixed rate on a notional amount, and receives from the
other counterparty the cumulative change in an inflation index over the
life of the swap. Both legs are settled at maturity, with no
intermediate cash flows during the life of the contract
(\citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, pp.
289--290}).

As in other swaps, to ensure that at inception it is an equitable
exchange of cash-flows, the fixed rate paid is determined such that the
swap has a zero present value when the contract is entered. This fixed
interest rate is the breakeven inflation rate, representing the market
consensus on the expected average annual inflation over the life of the
contract. (\citeproc{ref-James2023}{James \& Webber, 2023, pp. 34--35}).

There is only one cash flow for each leg (fixed and floating). As
presented by Ramirez (\citeproc{ref-Ramirez2015}{2015};
\citeproc{ref-Ramirez2015}{2015, p. 717}), we now provide the
calculation method for each of these cash flows:

\begin{equation}\phantomsection\label{eq-zcis-fixed-leg}{
\text{FixedLegCashFlow} = \text{N}\left[ (1 + X\%)^T - 1 \right]
}\end{equation}

\begin{equation}\phantomsection\label{eq-zcis-floating-leg}{
\text{FloatingLegCashFlow} = \text{N} \left( \frac{\text{I($T$)}_{\text{}}}{\text{I(0)}_{\text{}}} - 1 \right)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(N\): The notional
\item
  \(X\): The breakeven inflation rate
\item
  \(T\): The number of years to maturity
\item
  \(I(0)\): The inflation price index at the start or effective date
\item
  \(I(\)T\()\): The inflation price index at the payment date or
  maturity \(T\)
\end{itemize}

For monthly observations, when \(I(T)\) refers to the price index of a
given month (first calendar day of month), there is usually a lag of
several months between the payment date and the reference month from
which \(I(T)\) is observed and extracted. For example, in a UK
zero-coupon Retail Price Index (RPI) swap with a two month lag, if the
payment date is in October, then \(I(T)\) corresponds to the RPI value
published for August. The market practice is a lag of two months for the
UK market and three months for both Eurozone and the US inflation
markets. Note that the indices prices are published on a monthly basis
and usually two weeks after the end of the month.

However, if the final observation does not fall exactly on the first
calendar day of the payment month, then the final CPI price can be
derived using linear interpolation between two consecutive monthly index
values. In such cases, the end price index is given by the following
formula :

\begin{equation}\phantomsection\label{eq-index-interpolation}{
I_{lin}(T) = I(T) + \left( \frac{d - 1}{D} \right) (I(T+1M) - I(T))
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(d\): The day of the payment date (for example 15 for the 15th of the
  month)
\item
  \(D\): The number of calendar days in the payment month
\item
  \(I(T)\): The expected flat index value for payment date T
\item
  \(I(T+1M)\): The expected flat index value for payment date T + 1
  month
\item
  \(I_{lin}(T)\): The linearly interpolated price index at the payment
  date
\end{itemize}

Note that if a contract uses a flat index, the reference CPI is just the
most recently published monthly value, with no interpolation applied.

Most inflation swap derivatives are collateralized under ISDA Credit
Support Annex (CSA) agreements. Therefore, the present value of the
swap's single net cash flow can be computed using OIS discounting, with
a discount factor denominated in the contract's currency and matching
the maturity of the swap. As a result, the net present value (NPV) of a
zero-coupon inflation swap (ZCIS), in which the investor pays inflation
and receives a fixed amount at maturity \(T\), is given by:

\begin{equation}\phantomsection\label{eq-zcis-npv}{
\text{NPV|}_{\text{ZCIS}} = N \left( \frac{I(T)}{I(0)} - (1 + X)^T \right) P(0,T)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(P(0,T)\): The OIS discount factor applicable from today (\(t_0\) =
  0) to maturity \(T\)
\end{itemize}

\paragraph{Year-on-Year Inflation Swaps
(YoY)}\label{year-on-year-inflation-swaps-yoy}

We now turn our attention to another type of inflation swap: the
year-on-year (YoY) inflation swap. We assume that this type of inflation
swap is collateralized; therefore, the cash flows can be discounted
using OIS discount factors. We also assume that both legs have the same
payment frequency.

A year-on-year inflation swap is the exchange of cash flows on each
settlement date \(t_i\) (with \(i \in \{1, 2, \dots, T\})\), based on a
notional amount \(N\). One party pays, at time \(t_i\), a fixed amount
equal to the notional multiplied by the breakeven inflation rate \(X\),
which is not compounded over the life of the swap (unlike in a
zero-coupon inflation swap). The present value of the fixed leg is
therefore given by:

\begin{equation}\phantomsection\label{eq-yoy-fixed-leg}{
\text{PV}_{\text{Fixedleg}} = N X\sum_{i=1}^{T}  \tau_i P(0, t_i)
}\end{equation}

Where:

\begin{itemize}
\tightlist
\item
  \(P(t_0, t_i)\): The OIS discout factor applicable for \(t_i\)
\item
  \(\tau_i\): The day count fraction for time \(t_i\)
\end{itemize}

Note that in case the payment frequency of the fixed leg is annual,
discount fractions \(\tau_i\) are equal to 1

An investor paying the fixed interest receives, in return, the
year-on-year (YoY) inflation rate from the other party. This rate is
calculated as the percentage change in the relevant inflation index (as
defined in the contract) over each period \([t_{i-i}, t_{i} ]\). The
present value of the floating leg is therefore given by:

\begin{equation}\phantomsection\label{eq-yoy-floating-leg}{
\text{PV}_{\text{Floatingleg}} = N\sum_{i=1}^{T} \left( \frac{I(t_i)}{I(t_{i-1})} - 1 \right)P(0, t_i)
}\end{equation}

Like for ZCIS, if the fixing is not done on the first day of a month,
the CPI price is calculated by linear interpolation. Having described
the present value of both the fixed and floating legs, the price \(P\)
on valuation date \(t_0\) = 0, of a year-on-year (YoY) inflation swap,
from the side of an inflation receiver, can be factorised as follows:

\begin{equation}\phantomsection\label{eq-yoy-swap-pv}{
\text{P} = N\sum_{i=1}^{T}\left[ \left( \frac{I(t_i)}{I(t_{i-1})} - 1 \right) - X \times \tau_i \right]P(0, t_i)
}\end{equation}

Like zero-coupon inflation swaps, year-on-year inflation swaps are
quoted in the market based on their fixed rate \(X\). However,
zero-coupon inflation swaps (ZCIS) are more liquid and are considered as
the main benchmark instruments in the inflation derivatives market.

\paragraph{Applications}\label{applications-2}

The main objective of both zero-coupon and year-on-year inflation swaps
is to isolate, transfer inflation risk and therefore hedge against
inflation, as they allow market participants to exchange
inflation-linked payments for fixed payments, or vice versa. Market
participants can express a directional view on the future evolution of
inflation, relative to what is implied by the breakeven inflation rate.
If they believe inflation will be higher than what is currently priced
by the market, they may enter into an inflation receiver swap.
Conversely, if they expect inflation to be lower, they may enter into an
inflation payer swap (\citeproc{ref-James2023}{James \& Webber, 2023, p.
32}; also \citeproc{ref-Bouzoubaa2010}{Bouzoubaa \& Osseiran, 2010, p.
290}).

To illustrate further applications of inflation swaps, we also define
the main market participants, who can be categorized as either inflation
payers or inflation receivers depending on their hedging and investment
objectives.

Regarding inflation payers, participants include sovereigns, utility and
infrastructure companies, as well as real estate firms, because their
revenues are linked to inflation (\citeproc{ref-Ramirez2015}{Ramirez,
2015, pp. 709--710}). These participants' revenues are exposed to
inflation because their pricing structures are tied to it, like for
example, in the case of rents that are indexed to inflation, or oil's
regulated prices for sovereigns, where value-added tax (VAT) revenues
are proportional to the underlying commodity price. Therefore these
actors are naturally long inflation. However, even if rising inflation
increases their revenues, they prefer to lock in predictable, stable
cash flows and make financial planning easier. As a result, inflation
payers are ready to pay inflation and received a fixed interest rate
(the breakeven inflation rate or expected annual average rate that
reflects how much inflation the market expects over the life of the swap
(\citeproc{ref-James2023}{James \& Webber, 2023, pp. 19--20})) in order
to mitigate the risk of unexpectedly high inflation.

Inflation receiver participants include pension funds, insurance
companies, asset managers, and retail investors
(\citeproc{ref-Ramirez2015}{Ramirez, 2015, p. 710}). For example, retail
investors and asset managers (investing on behalf of clients) may want
to protect the real value and purchasing power of their savings, as well
as the real returns of their portfolios under management, which can be
eroded by inflation. Therefore, they may enter into an inflation
receiver swap to receive the inflation leg, which can be beneficial in a
rising inflation environment. For institutional participants such
pension funds and insurance companies, receiving inflation helps them
match their inflation-linked liabilities. These liabilities, like for
example future pension payments or insurance claims, increase with
inflation. By receiving the inflation leg in a swap, they can better
align their income with these rising obligations and reduce the risk of
a mismatch.

Finally, there are also market participants who do not have a natural
need to pay or receive inflation. These include investment banks, which
take inflation positions to meet their clients' hedging needs, and hedge
funds, which may choose to pay or receive inflation based on their
views.

\section{Research}\label{research}

\subsection{Methodology}\label{methodology}

The goal of the research section is to replicate the Bloomberg pricing
of the in-scope instruments using the theoretical models presented in
the literature review, with the help of the Python programming language.
Each instrument is priced in Python using the model previously discussed
in its corresponding section. The objective is to assess the accuracy of
the models by comparing the theoretical prices to observed market prices
in Bloomberg. This comparison also aims to identify and discuss any
pricing discrepancies that may arise between the Python implementation
and Bloomberg's valuations. In the results section, we present the
Python code along with the relevant Bloomberg screenshots for each
instrument. We also specify the functions used to retrieve the Bloomberg
data.

Note that the value of any financial instrument is determined by the
expected cash flows it generates, discounted at an appropriate rate. To
simplify the discounting process in this research, we use a single OIS
curve: the USD SOFR swap curve. In our Python implementation, we
bootstrapped USD SOFR swap rates retrieved from Bloomberg as of May 23,
2025, to obtain zero rates and their corresponding continuously
compounded discount factors. These discount factors were then used to
discount the cash flows of both fixed income and derivative instruments.

In the following section, we present an overview of the data used and
the model implemented for each instrument.

\subsection{Required Data and Model}\label{required-data-and-model}

The field stage begins with the collection of observable prices and
market data from the Bloomberg Terminal. Bloomberg is used as the data
source in this research because it is reliable and widely used in the
financial industry. All market data and prices used in the analysis have
been extracted on the same day (as of 05/23/25) to ensure consistency
and accuracy in the calculations. Below, we present the data required to
price each instrument, along with the corresponding model implemented in
the Python code.

For \textbf{stock}, we chose to price Apple stock, as it is a globally
well-known company with available and reliable financial data. We valued
Apple using the Discounted Cash Flow (DCF) model, projecting the Free
Cash Flow to the Firm (FCFF) over a 5-year explicit forecast period
(with a constant annual growth) and compute a terminal value, assuming a
terminal constant growth rate \(g\). We estimated a fair value for Apple
stock based directly on the WACC computed by Bloomberg, which we
extracted using the ``WACC'' function. Non-operating assets value was
retrieved from Apple's 2024 consolidated financial statements. Total
non-equity liabilities were proxied using the total debt figure provided
by Bloomberg through the ``FA'' (``Financial Analysis'') function.
Finally, using the ``DES'' (``Description'') function, we extracted the
number of shares outstanding as of 05/23/2025.

For \textbf{bond}, the discounted free cash flow (DCF) model has been
also used and we priced one government bond. Using Bloomberg's ``DES''
function, we extracted key data such as the issue date, maturity date,
market price as of a specific valuation date (here 23/05/2025), the
coupon rate, the day count convention (used for calculating accrued
interest) and the payment frequency. To determine the price of a bond,
the maturity date is important because it defines the number of
remaining coupon payments the investor will receive. Since different
investors may have different investment horizons, we could get many
different prices for the same bond. However, in market practice, it is
generally accepted that the price of a bond is based on all remaining
coupon payments and the full repayment of principal at maturity. In this
study, we will therefore adopt this approach and price bonds assuming
they are held until maturity.

For the pricing of a \textbf{vanilla option} with a European exercise
style (exercisable only at maturity), key inputs include the underlying
stock entity (we chose Apple) and its spot price, the strike price, time
to expiry, and the risk-free interest rate. The Black-Scholes model is
usually used to price European options that do not pay dividends.
However, when the underlying asset pays dividends (which is the case for
Apple), we used the Black-Scholes model with a continuous dividend yield
\(q\) to adjust the cost of carry \(b\) and to account for the impact of
dividends on the underlying asset's value. For a given strike and time
to maturity, we relied on the risk-free rate, implied dividend and
implied volatility (in percent) provided directly by Bloomberg using the
``OMON'' (``Option Monitor'') function. The observed option prices on
Apple have been captured using the same function. Note that for options
on US equity, the market practice is to use the Act/365 day count
convention. We also provide a Python illustration of Newton's method to
approximate and validate the implied volatility returned by the
Bloomberg terminal.

For pricing a \textbf{fixed-for-floating receiver interest rate swap},
we needed to specify the notional amount of the contract, the start date
of the contract, the termination date of the contract (maturity), the
fixed rate to be received over the life of the swap and the payment
frequency by which the cashflows are calculated. In our calculation for
the floating leg, the reference index is the overnight USD SOFR rate (as
a result, the reset frequency, by which the floating rate index is
reset, is daily). The day count convention used is ACT/360, and the
currency of the notional is USD for both legs. The zero-coupon rates
used to discount the swap's cash flows have been obtained using our own
Python code, by bootstrapping the SOFR swap rates retrieved from
Bloomberg via the ``SWPM'' (``Swap Manager'') function. The net cash
flows received and paid on each payment date are then discounted using
the appropriate discount factors to determine their present value. We
compared the resulting net present value generated by our pricing code
with the results provided by Bloomberg in the discussion section.

\textbf{Inflation swaps} are priced using data such as the notional
amount, maturity, inflation lag, interpolation method (monthly), payment
frequency, and day count convention. The inflation index chosen for the
inflation paying leg is the CPUNSA (Consumer Price Index for All Urban
Consumers, Not Seasonally Adjusted). We only implemented a Python
pricing code for a five-year receiver zero-coupon inflation swap (ZCIS),
whose fixed leg is priced using the mid breakeven inflation rate or the
annualized inflation rate over the life of the contract, provided
directly by Bloomberg (via the ``SWPM'' function). The floating leg is
based on the cumulative inflation over the same period, calculated using
the ratio of the projected CPI at maturity (taking into consideration
the lag) to the CPI observed at the start of the swap. We also applied a
quadratic interpolation to estimate the reference CPI index level at
maturity, since the projected CPI mid values from Bloomberg are only
provided on an annual basis. The indexation lag applied is 3 months.

Regarding \textbf{swaptions}, we applied the Black model, which assumes
that the underlying forward swap rates follow a lognormal distribution.
The key inputs include the option type (European), the option's
expiration date, the tenor of the underlying swap, the effective and
maturity dates of the swap, the notional amount (in USD), and the strike
rate. A constant implied volatility is also required as an input, and we
use the one provided by Bloomberg. The swaption we priced has a physical
delivery. We compare the net present value (NPV) of the swaption
returned by Bloomberg (via the ``SWPM'' function) with the value
calculated using our own Python pricing implementation in the discussion
section.

\subsection{Results}\label{results}

To illustrate some of the pricing formulas described above, we attempt
below to reproduce market prices observed on Bloomberg using our own
pricing functions implemented in Python.

In the following sections, we describe how we obtained market data from
Bloomberg, and then attempt to replicate the prices.

For readability, the code from the library (finlib) we use, and have
developed for this purpose, is provided in the appendix.

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\OperatorTok{\%}\NormalTok{load\_ext autoreload}
\ImportTok{import}\NormalTok{ finlib }\ImportTok{as}\NormalTok{ fl}
\ImportTok{import}\NormalTok{ numpy }\ImportTok{as}\NormalTok{ np}
\end{Highlighting}
\end{Shaded}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\OperatorTok{\%}\NormalTok{autoreload }\DecValTok{2}
\end{Highlighting}
\end{Shaded}

\subsubsection{Stock}\label{stock}

In this section, we aim to replicate the pricing of Apple's stock as
observed on May 23, 2025, by applying the enterprise discounted cash
flow method using Python. We retrieved the observed market price
(\$195.27) using the function ``DES'' (``Description'') as follows:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Apple_stock_data/Apple_General_info.png}}

}

\caption{Apple Financial Overview (Source: Bloomberg Terminal)}

\end{figure}%

In this valuation model, Free Cash Flows to Firm (FCFF) are projected
over a 5-year explicit forecast period. By projecting Apple's free cash
flows over a five-year explicit forecast period, we implicitly assume
that the company will transition into a stable growth phase at the end
of this horizon. Apple is a large, mature, and highly profitable firm,
which means it has stable and predictable cash flows that can be
reasonably projected over a five-year period. After the explicit
forecast period, a terminal value is used to capture the firm's
long-term cash generating capacity which is appropriate for a company
like Apple that is expected to continue operating and creating value
beyond the next five years.

After the explicit forecast period, a terminal value is used to capture
the company's long-term cash generating capacity, as Apple is expected
to continue operating and creating value beyond the next five years. We
set a 4\% terminal growth rate, as we believe Apple's free cash flows to
firm can continue to grow at a rate consistent with long-term global
economic growth plus inflation. This reflects the fact that Apple
benefits from a strong and durable competitive advantage, supported by
its globally recognized brand and a highly loyal customer base. This
terminal growth rate may appear optimistic at first glance, but it
remains conservative when compared to the historical average growth of
Apple's free cash flows over the past five years.

Regarding the Weighted Average Cost of Capital (WACC), we used the value
provided by Bloomberg with help of the ``WACC'' function. As shown
below, the terminal reported a WACC of 9.3\%:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Apple_stock_data/WACC.png}}

}

\caption{WACC (Source: Bloomberg Terminal)}

\end{figure}%

For projecting Apple's free cash flows over the next five years, we
started from the most recent known annual free cash flow, which,
according to Yahoo Finance, corresponds to the fiscal year ending on
September 30, 2024, with a reported value of \$108.807 billion.

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Apple_stock_data/Free_cash_flows_Yahoo.png}}

}

\caption{Apple Free Cash Flows (Source: Bloomberg Terminal)}

\end{figure}%

Regarding non-operating assets, we use the reported balance sheet values
(on an annual basis, in millions of USD) as of September 28, 2024, from
Apple's financial statements. The total value of non-operating assets
amounts to \$156.65 billion, which includes \$91.479 billion in
non-current marketable securities, \$35.228 billion in current
marketable securities, and \$29.943 billion in cash and cash
equivalents, as shown below:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Apple_stock_data/Balance_sheet.png}}

}

\caption{Apple -- Balance Sheet Assets (Source: Bloomberg Terminal)}

\end{figure}%

Finally, we used the `Total Debt' figure as of 05/23/2025, provided by
Bloomberg, as a proxy for Apple's total non-equity liabilities or
claims. The reported value is:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Apple_stock_data/Total_debt.png}}

}

\caption{Apple Non Operating Assets (Source: Bloomberg Terminal)}

\end{figure}%

While exact growth figures may vary by source, several financial data
websites estimate the five-year compound annual growth rate (CAGR) of
Apple's free cash flow to be approximately 8\%. We therefore assume, to
simplify the pricing implementation, that Apple will be able to
replicate this level of performance on an annual basis over the explicit
forecast period, which is consistent with the company's strong
fundamentals. We assume that, on average, Apple's free cash flows to the
firm will grow at 8\% annually over the next five years. However, it
should be noted that this assumption is not realistic in practice, as
growth is not linear but usually fluctuates over time. This assumption
will be discussed in detail in the dedicated section.

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{fcff\_initial }\OperatorTok{=} \FloatTok{108.807}

\CommentTok{\# Assumed annual growth rates for FCFF over the next 5 years}
\CommentTok{\# (8 \% each year)}
\NormalTok{growth\_rates }\OperatorTok{=}\NormalTok{ [}\FloatTok{0.08}\NormalTok{, }\FloatTok{0.08}\NormalTok{, }\FloatTok{0.08}\NormalTok{, }\FloatTok{0.08}\NormalTok{, }\FloatTok{0.08}\NormalTok{]}

\CommentTok{\# Weighted Average Cost of Capital (WACC), used as the}
\CommentTok{\# discount rate}
\NormalTok{wacc }\OperatorTok{=} \FloatTok{0.093}

\CommentTok{\# Terminal growth rate after the forecast period}
\NormalTok{g }\OperatorTok{=} \FloatTok{0.04}

\CommentTok{\# Value of non{-}operating assets}
\NormalTok{non\_operating\_assets }\OperatorTok{=} \FloatTok{156.65}

\CommentTok{\# Call the function enterprise\_value from our library}
\NormalTok{ev }\OperatorTok{=}\NormalTok{ fl.enterprise\_value(fcff\_initial, growth\_rates, wacc, g,}
\NormalTok{                         non\_operating\_assets)}

\CommentTok{\# Print the estimated Enterprise Value}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Estimated Enterprise Value: $}\SpecialCharTok{\{}\NormalTok{ev}\SpecialCharTok{:,.2f\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Estimated Enterprise Value: $2,692.67
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{non\_equity\_liabilities }\OperatorTok{=} \FloatTok{98.186}  \CommentTok{\# total non{-}equity liabilities}
\NormalTok{shares\_outstanding }\OperatorTok{=} \FloatTok{14.935}

\NormalTok{eq\_val }\OperatorTok{=}\NormalTok{ fl.equity\_value(ev, non\_equity\_liabilities)}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Estimated Equity Value: $}\SpecialCharTok{\{}\NormalTok{eq\_val}\SpecialCharTok{:,.2f\}}\SpecialStringTok{"}\NormalTok{)}
\BuiltInTok{print}\NormalTok{(}\StringTok{"Estimated Share Price: "}
      \SpecialStringTok{f"$}\SpecialCharTok{\{}\NormalTok{eq\_val }\OperatorTok{/}\NormalTok{ shares\_outstanding}\SpecialCharTok{:,.2f\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Estimated Equity Value: $2,594.49
Estimated Share Price: $173.72
\end{verbatim}

\subsubsection{European options}\label{european-options}

We chose to price a European call and put options on Apple Inc.~stock.
To extract market option prices, we used the Bloomberg terminal function
``OMON'' (Option Monitor). This function displays, for both calls and
puts, several maturity tranches, each with its corresponding expiration
date, remaining days to maturity, implied dividend yield (``IDiv''),
risk-free rate (``R''), and implied forward price (``IFwd'').

Within each tranche, the bid and ask prices, last traded price, implied
volatility (``IVM''), and trading volume are displayed for various
options across different strike price levels. We retrieved the data as
of May 23, 2025. On that date, the spot price of the underlying Apple
stock was \$195.27. Below, we provide a screenshot of observed option
market prices for Apple Inc:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Option_prices_Apple/Option_prices_1.png}}

}

\caption{Observed Market Prices for Apple Inc.~Options as of 23/05/2025
(Source: Bloomberg Terminal)}

\end{figure}%

We now attempt to replicate the pricing of a call and a put option,
respectively AAPL 01/16/26 C205 (line 40) and AAPL 12/19/25 P205 (line
130). Note that we use the Actual/365 day count convention because it is
the standard market practice for options on US equity. Bloomberg
provides us with the actual number of remaining days to expiry,
calculated from May 24, 2025.

We first convert the discrete dividend into a continuous dividend yield
\(q\), then fill in the necessary inputs and finally call the function
gblack\_scholes from our library \(fl\) in order to calculate the
theoretical option price using the Generalized Black-Scholes formula.
The necessary input data are specified before calling the function from
our `finlib' library.

The code provided by Haug (\citeproc{ref-Haug2007}{2007}) was originally
written in Excel VBA and has been converted into Python for our
implementation.

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\CommentTok{\# line 40 Pricing of AAPL 1/16/26 C205}

\CommentTok{\# The expected dividend payments over the life of the option}
\NormalTok{d }\OperatorTok{=} \FloatTok{0.11}  \CommentTok{\#}

\CommentTok{\# We compute the dividend yield based on Bloomberg implied}
\CommentTok{\# dividend  ("IDiv")}
\NormalTok{q }\OperatorTok{=} \OperatorTok{{-}}\NormalTok{np.log(}\DecValTok{1} \OperatorTok{{-}}\NormalTok{ d }\OperatorTok{/} \DecValTok{205}\NormalTok{)}

\CommentTok{\# Input parameters for for the Generalized Black{-}Scholes model:}

\NormalTok{call\_put\_flag }\OperatorTok{=} \StringTok{"c"}
\NormalTok{S }\OperatorTok{=} \FloatTok{195.27}  \CommentTok{\# Spot price of the underlying as of May 23, 2025}
\NormalTok{x }\OperatorTok{=} \DecValTok{205}  \CommentTok{\# Strike price}
\NormalTok{T }\OperatorTok{=} \DecValTok{238} \OperatorTok{/} \DecValTok{365}  \CommentTok{\# ACT/365 day count used}
\NormalTok{r }\OperatorTok{=} \FloatTok{0.0447}  \CommentTok{\# Risk{-}free interest rate (R)}
\NormalTok{b }\OperatorTok{=}\NormalTok{ r }\OperatorTok{{-}}\NormalTok{ q  }\CommentTok{\# cost of carry}
\NormalTok{v }\OperatorTok{=} \FloatTok{0.3042}  \CommentTok{\# Implied volatility (30.42\%), }
\CommentTok{\# from Bloomberg column "IVM"}

\CommentTok{\# Call the Generalized Black{-}Scholes function}
\NormalTok{price }\OperatorTok{=}\NormalTok{ fl.gblack\_scholes(call\_put\_flag, S, x, T, r, b, v)}

\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Option Price (Call): }\SpecialCharTok{\{}\NormalTok{price}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Option Price (Call): 17.379920355151867
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\CommentTok{\# line 130 Pricing of AAPL 12/19/25 P205}

\NormalTok{d }\OperatorTok{=} \FloatTok{0.11}
\NormalTok{q }\OperatorTok{=} \OperatorTok{{-}}\NormalTok{np.log(}\DecValTok{1} \OperatorTok{{-}}\NormalTok{ d }\OperatorTok{/}\NormalTok{ x)}

\NormalTok{call\_put\_flag }\OperatorTok{=} \StringTok{"p"}
\NormalTok{S }\OperatorTok{=} \FloatTok{195.27}
\NormalTok{x }\OperatorTok{=} \DecValTok{205}
\NormalTok{T }\OperatorTok{=} \DecValTok{210} \OperatorTok{/} \DecValTok{365}
\NormalTok{r }\OperatorTok{=} \FloatTok{0.0451}
\NormalTok{b }\OperatorTok{=}\NormalTok{ r }\OperatorTok{{-}}\NormalTok{ q  }\CommentTok{\# r{-}q}
\NormalTok{v }\OperatorTok{=} \FloatTok{0.3111}

\NormalTok{price }\OperatorTok{=}\NormalTok{ fl.gblack\_scholes(call\_put\_flag, S, x, T, r, b, v)}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Option Price (Put): }\SpecialCharTok{\{}\NormalTok{price}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Option Price (Put): 20.9048457363851
\end{verbatim}

For the AAPL 1/16/26 C205 call option, we aim to test the consistency of
our pricing code and verify whether the implied volatility provided by
Bloomberg can be recovered using a python implementation of the Newton's
method. We use the theoretical Black-Scholes call price previoulsy
obtained as input and apply the Newton's method to recover the Bloomberg
implied volatility. If our implementation is correct, we should recover
the same implied volatility that was initially used, which is indeed the
case below:

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{call\_put\_flag }\OperatorTok{=} \StringTok{"c"}

\CommentTok{\# The theoretical call price previously computed using }
\CommentTok{\# the Black{-}Scholes formula}
\NormalTok{market\_price }\OperatorTok{=}\NormalTok{ price }\OperatorTok{=} \FloatTok{17.379920355151867}

\NormalTok{S }\OperatorTok{=} \FloatTok{195.27}
\NormalTok{x }\OperatorTok{=} \DecValTok{205}
\NormalTok{T }\OperatorTok{=} \DecValTok{238} \OperatorTok{/} \DecValTok{365}
\NormalTok{r }\OperatorTok{=} \FloatTok{0.0447}
\NormalTok{b }\OperatorTok{=}\NormalTok{ r }\OperatorTok{{-}}\NormalTok{ q}

\NormalTok{implied\_volatility }\OperatorTok{=}\NormalTok{ fl.implied\_volatility(}
\NormalTok{    market\_price, S, x, T, r, b, call\_put\_flag,}
\NormalTok{    initial\_vol\_guess}\OperatorTok{=}\FloatTok{0.1}\NormalTok{,  }\CommentTok{\# Starting guess}
\NormalTok{    tol}\OperatorTok{=}\FloatTok{1e{-}6}\NormalTok{,  }\CommentTok{\# Convergence tolerance}
\NormalTok{    max\_iter}\OperatorTok{=}\DecValTok{100}\NormalTok{,}
\NormalTok{)  }\CommentTok{\# Maximum number of iterations}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Option Implied Volatility (Call): }\SpecialCharTok{\{}\NormalTok{implied\_volatility}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Option Implied Volatility (Call): 0.3041999999999995
\end{verbatim}

We attempted to reproduce the pricing of the AAPL 01/16/2026 C205 call
option using the Bloomberg option pricer, with help of the ``OVME''
(Option Valuation Model Equity) function, in order to obtain as output
the Bloomberg associated Greeks. We then implemented the Greeks in
Python to replicate the values provided by Bloomberg, as shown below:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Option_prices_Apple/Greeks.png}}

}

\caption{Greeks - AAPL 01/16/2026 C205 (Source: Bloomberg Terminal)}

\end{figure}%

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{delta }\OperatorTok{=}\NormalTok{ fl.gdelta(call\_put\_flag, S, x, T, r, b, v)}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Option Delta (Call): }\SpecialCharTok{\{}\NormalTok{delta }\OperatorTok{*} \DecValTok{100}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Option Delta (Call): 51.84303746100164
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{gamma }\OperatorTok{=}\NormalTok{ fl.ggamma(S, T, r, b, v, delta)}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Option Gamma (Call): }\SpecialCharTok{\{}\NormalTok{gamma }\OperatorTok{*}\NormalTok{ S }\OperatorTok{/} \DecValTok{100} \OperatorTok{*} \DecValTok{100}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Option Gamma (Call): 1.585780419619033
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{vega }\OperatorTok{=}\NormalTok{ fl.gvega(S, x, T, r, b, v)}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Option Vega (Call): }\SpecialCharTok{\{}\NormalTok{vega }\OperatorTok{/} \DecValTok{100}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Option Vega (Call): 0.6281490120878018
\end{verbatim}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{theta }\OperatorTok{=}\NormalTok{ fl.gtheta(call\_put\_flag, S, x, T, r, b, v)}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Option Theta (Call): }\SpecialCharTok{\{}\NormalTok{theta }\OperatorTok{/} \DecValTok{365}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Option Theta (Call): -0.05112131691868341
\end{verbatim}

\subsubsection{Bootstrap}\label{bootstrap}

In this section, we bootstrap zero coupon rates using python, based on
USD SOFR swap rates extracted from the Bloomberg Terminal with help of
the ``SWPM'' (Swap Manager) function.

As input, we therefore provide all the market par swap rates observed as
of May 23, 2025, across maturities up to fifty years, except for tenors
shorter than 12 months. This is because tenors below 1 year are more
sensitive to short-term market fluctuations and are less relevant for
constructing a smooth long-term curve. As a result, by removing
short-term tenors, we mitigate the influence of short-term market noise
and obtain a smoother and more consistent bootstrapped curve.

The swap rates are shown in the excel screenshot below, in column B. The
bootstrap\_zero\_rates function returns a list of tenors along with
their corresponding continuously compounded zero rates, computed using a
quarterly frequency (0.25) and quadratic interpolation.

Note that we use the obtained zero rates to compute the corresponding
continuously compounded discount factors using the discount function df
described in the next section.

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/IRS/swap_rates.png}}

}

\caption{Market Swap rates (Source: Bloomberg Terminal)}

\end{figure}%

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{swap\_tenors }\OperatorTok{=}\NormalTok{ np.array(}
\NormalTok{    [}
        \FloatTok{1.0}\NormalTok{, }\FloatTok{2.0}\NormalTok{, }\FloatTok{3.0}\NormalTok{, }\FloatTok{4.0}\NormalTok{, }\FloatTok{5.0}\NormalTok{, }\FloatTok{6.0}\NormalTok{, }\FloatTok{7.0}\NormalTok{, }\FloatTok{8.0}\NormalTok{, }\FloatTok{9.0}\NormalTok{, }\FloatTok{10.0}\NormalTok{,}
        \FloatTok{12.0}\NormalTok{, }\FloatTok{15.0}\NormalTok{, }\FloatTok{20.0}\NormalTok{, }\FloatTok{25.0}\NormalTok{, }\FloatTok{30.0}\NormalTok{, }\FloatTok{40.0}\NormalTok{, }\FloatTok{50.0}\NormalTok{,}
\NormalTok{    ]}
\NormalTok{)}
\NormalTok{swap\_rates }\OperatorTok{=}\NormalTok{ np.array(}
\NormalTok{    [}
        \FloatTok{0.0408605}\NormalTok{, }\FloatTok{0.037523}\NormalTok{, }\FloatTok{0.036634}\NormalTok{, }\FloatTok{0.0366275}\NormalTok{, }\FloatTok{0.03691}\NormalTok{, }
        \FloatTok{0.03742}\NormalTok{, }\FloatTok{0.037955}\NormalTok{, }\FloatTok{0.038485}\NormalTok{, }\FloatTok{0.0389775}\NormalTok{, }\FloatTok{0.039444}\NormalTok{, }
        \FloatTok{0.0403055}\NormalTok{, }\FloatTok{0.0412308}\NormalTok{, }\FloatTok{0.041905}\NormalTok{, }\FloatTok{0.0417239}\NormalTok{, }\FloatTok{0.04122}\NormalTok{, }
        \FloatTok{0.0397575}\NormalTok{, }\FloatTok{0.0382664}\NormalTok{,}
\NormalTok{    ]}
\NormalTok{)}

\NormalTok{tenors, zero\_rates }\OperatorTok{=}\NormalTok{ fl.bootstrap\_zero\_rates(}
\NormalTok{    swap\_tenors, swap\_rates, period}\OperatorTok{=}\FloatTok{0.25}
\NormalTok{)}

\NormalTok{n }\OperatorTok{=} \DecValTok{5}

\CommentTok{\# Print the first n zero{-}coupon tenors}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"First }\SpecialCharTok{\{}\NormalTok{n}\SpecialCharTok{\}}\SpecialStringTok{ zero coupon tenors }\SpecialCharTok{\{}\NormalTok{tenors[:n]}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}

\CommentTok{\# Print the first n zero{-}coupon rates}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"First }\SpecialCharTok{\{}\NormalTok{n}\SpecialCharTok{\}}\SpecialStringTok{ zero coupon rates }\SpecialCharTok{\{}\NormalTok{zero\_rates[:n]}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
First 5 zero coupon tenors [0.25 0.5  0.75 1.   1.25]
First 5 zero coupon rates [0.0408605  0.04065215 0.04065251 0.04065268 0.03955455]
\end{verbatim}

We plot below the resulting zero curve.

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{memoire_files/figure-pdf/cell-14-output-1.png}}

}

\caption{USD SOFR Bootstrapped Zero-Rate Curve}

\end{figure}%

\subsubsection{Discount function}\label{discount-function}

In this section, we introduce the Python implementation of the discount
function new\_df. This function takes as input an array of tenors and
their corresponding zero rates obtained from the bootstrap function, and
returns the continuously compounded discount factor for any given time
\(t\), also using quadratic interpolation. Note that the code for the
discount function is provided in the Appendix. We also show an example
of output below for time 1.5:

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\CommentTok{\# We call the new\_df function from our \textquotesingle{}finlib\textquotesingle{} library}
\NormalTok{df }\OperatorTok{=}\NormalTok{ fl.new\_df(tenors, zero\_rates)}
\end{Highlighting}
\end{Shaded}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{df(}\FloatTok{1.5}\NormalTok{) }\CommentTok{\# example for time 1.5}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
np.float64(0.9437068155652936)
\end{verbatim}

\subsubsection{Bonds}\label{bonds}

In this section, we attempt to replicate the pricing of a government
bond, specifically, a U.S. Treasury bond (with ISIN: US912810FM54),
which was issued on 02/15/2000. The instrument is denominated in USD,
and we use also use discount factors expressed in USD. We used the
``DES'' (``Description'') function in the Blommberg terminal to retrieve
all the necessary bond data.

According to the screenshot below, the bond pays a 6.250\% annual
coupon, but the payment frequency is semi-annual (as mentioned by the
field ``Cpn Frequency: S/A''), which means it pays 3.125\% every six
months. We can also that the bond follows the ACT/ACT day count
convention, which is the standard convention used for U.S. Treasuries.
The maturity date is May 15, 2030, and the data was captured as of May
23, 2025. As a result, there are ten remaining coupon payments, each of
which is discounted using its corresponding risk-free zero rate. Below,
we provide a screenshot of the `DES' function from the Bloomberg
Terminal, which displays all the relevant information regarding this
bond:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Bonds/US_treasury_bond.jpg}}

}

\caption{US Treasury Bond (Source: Bloomberg Terminal)}

\end{figure}%

The price of the U.S. Treasury bond, as shown in the Bloomberg Terminal
screenshot, is quoted as `110-10', which according to the standard
Treasury market convention, corresponds to 110 plus 10/32, or 110.3125
in decimal form.

We call the bond\_price function from our library, which returns the
clean price of the Treasury bond, taking into account the coupon payment
dates and coupon rates. Note that we can discount these Treasury bond
cash flows using SOFR-based zero rates, as SOFR, which reflects the cost
of overnight borrowing collateralized by U.S. Treasuries, is considered
by the market as a reliable proxy for the risk-free rate. Furthermore,
the US government is perceived as default-free. As a result, the
discount factors used reflect the low credit risk profile of the entity
that issued this bond instrument.

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\CommentTok{\# create an array of notionals of 100 for each payment}
\NormalTok{notionals }\OperatorTok{=} \FloatTok{10e1} \OperatorTok{*}\NormalTok{ np.ones(}\DecValTok{10}\NormalTok{)}

\CommentTok{\# We define the semi{-}annual coupon payment times}
\NormalTok{coupon\_times }\OperatorTok{=}\NormalTok{ np.array([}\FloatTok{0.5}\NormalTok{, }\DecValTok{1}\NormalTok{, }\FloatTok{1.5}\NormalTok{, }\DecValTok{2}\NormalTok{, }\FloatTok{2.5}\NormalTok{, }\DecValTok{3}\NormalTok{, }\FloatTok{3.5}\NormalTok{, }\DecValTok{4}\NormalTok{, }\FloatTok{4.5}\NormalTok{, }\DecValTok{5}\NormalTok{])}

\CommentTok{\# We define the fixed cash{-}flows}
\NormalTok{fixed\_rates }\OperatorTok{=}\NormalTok{ (}
\NormalTok{    np.array([}\FloatTok{3.125}\NormalTok{, }\FloatTok{3.125}\NormalTok{, }\FloatTok{3.125}\NormalTok{, }\FloatTok{3.125}\NormalTok{, }\FloatTok{3.125}\NormalTok{, }\FloatTok{3.125}\NormalTok{, }\FloatTok{3.125}\NormalTok{, }
              \FloatTok{3.125}\NormalTok{, }\FloatTok{3.125}\NormalTok{, }\FloatTok{103.125}\NormalTok{])}
    \OperatorTok{/} \FloatTok{100.0}
\NormalTok{)}

\CommentTok{\# This line computes the NPV of the bond using the function}
\CommentTok{\# provided in Appendix}
\NormalTok{bond\_npv }\OperatorTok{=}\NormalTok{ fl.bond\_price(df, notionals, coupon\_times, fixed\_rates)}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Clean Price: }\SpecialCharTok{\{}\NormalTok{bond\_npv}\SpecialCharTok{\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Clean Price: 111.49814541225314
\end{verbatim}

To compute the dirty price as of May 23, 2025, we need to add the
accrued interest for the period from May 16 to May 23, which corresponds
to 8 calendar days, assuming the ACT/ACT day count convention used by
the market. Furthermore, there are exactly 365 calendar days in 2025. As
a result, the accrued interest is:

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\NormalTok{dcf }\OperatorTok{=} \DecValTok{8} \OperatorTok{/} \DecValTok{365}  \CommentTok{\# There are exactly 365 calendar days in 2025}
\NormalTok{fixed\_rate }\OperatorTok{=} \FloatTok{0.03125}
\NormalTok{AI }\OperatorTok{=}\NormalTok{ fixed\_rate }\OperatorTok{*}\NormalTok{ dcf  }\CommentTok{\# AI is Accrued Interest}
\BuiltInTok{print}\NormalTok{(AI)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
0.0006849315068493151
\end{verbatim}

and the dirty price is therefore :

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\CommentTok{\# To obtain the dirty price, we must add the accrued interest (AI) }
\CommentTok{\# to the clean price}
\BuiltInTok{print}\NormalTok{(bond\_npv }\OperatorTok{+}\NormalTok{ AI)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
111.**************
\end{verbatim}

\subsubsection{Interest Rate Swaps}\label{interest-rate-swaps-1}

We now aim to replicate the pricing of an interest rate swap, more
specifically, a single-currency USD SOFR receiver swap. Since most SOFR
swaps are collateralized under a Credit Support Annex (CSA) agreement,
we can use SOFR-based discount factors to discount the cash flows of
both the fixed and floating legs of the swap.

We used Bloomberg's interest rate swap pricer with the ``SWPM'' (Swap
Manager) function. We chose to price a swap with a 4.5-year maturity,
where both legs have the same semi-annual payment frequency.

The swap is priced as of May 23, 2025, which corresponds to the date we
retrieved the SOFR par swap rates used to compute the zero coupon rates
and the associated discount factors. Note that the curve date and the
valuation date are the same on the below Bloomberg screenshot, ensuring
consistency in pricing.

The notional amount of the swap is 10 million USD. When all these
informations are input into Bloomberg, the system calculates the par
fixed rate, which is the fixed rate that sets the net present value
(NPV) of the swap to zero at inception. As a result, the value of the
swap shown in the screenshot is approximately zero, indicating that the
interest rate swap (IRS) is fairly priced. The floating leg is indexed
to USD SOFR (Secured Overnight Financing Rate), and the day count
convention applied is ACT/360. We provide below a screenshot
illustrating the pricing of this interest rate swap (IRS):

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/IRS/IRS.png}}

}

\caption{SOFR Swap Pricing (Source: Bloomberg Terminal)}

\end{figure}%

We provide below the code that defines the required inputs and calls our
swap pricing function. Note that the implementation of the pricing
function is provided in the Appendix for clarity and readability.

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\CommentTok{\# We define the payment times (in years) for the fixed leg of }
\CommentTok{\# the swap}
\NormalTok{fixed\_times }\OperatorTok{=}\NormalTok{ np.array(}
\NormalTok{    [}\FloatTok{0.5}\NormalTok{, }\FloatTok{1.0}\NormalTok{, }\FloatTok{1.5}\NormalTok{, }\FloatTok{2.0}\NormalTok{, }\FloatTok{2.5}\NormalTok{, }\FloatTok{3.0}\NormalTok{,}
     \FloatTok{3.5}\NormalTok{, }\FloatTok{4.0}\NormalTok{, }\FloatTok{4.5}\NormalTok{]}
\NormalTok{)  }\CommentTok{\# 9 payment dates but 8 periods}

\CommentTok{\# We define here the fixed par coupon rate for each period}
\CommentTok{\# We have 8 fixed rates, each applying to one of the 8 accrual }
\CommentTok{\# periods between payment dates.}
\NormalTok{fixed\_rates }\OperatorTok{=}\NormalTok{ np.array(}
\NormalTok{    [}
        \FloatTok{0.03644199}\NormalTok{, }\FloatTok{0.03644199}\NormalTok{, }\FloatTok{0.03644199}\NormalTok{, }\FloatTok{0.03644199}\NormalTok{,}
        \FloatTok{0.03644199}\NormalTok{, }\FloatTok{0.03644199}\NormalTok{, }\FloatTok{0.03644199}\NormalTok{, }\FloatTok{0.03644199}\NormalTok{,}
\NormalTok{    ]}
\NormalTok{)}

\CommentTok{\# We define the notionals for the fixed leg —}
\CommentTok{\# here USD 10 million for each period}
\NormalTok{fixed\_notionals }\OperatorTok{=} \FloatTok{10e6} \OperatorTok{*}\NormalTok{ np.ones(}\BuiltInTok{len}\NormalTok{(fixed\_rates))  }\CommentTok{\# 8 notionals}

\CommentTok{\# We define the payment times (in years) for the floating leg }
\CommentTok{\# (same payment frequency as fixed leg, 9 times, 8 periods)}
\NormalTok{floating\_times }\OperatorTok{=}\NormalTok{ np.array([}\FloatTok{0.5}\NormalTok{, }\FloatTok{1.0}\NormalTok{, }\FloatTok{1.5}\NormalTok{, }\FloatTok{2.0}\NormalTok{, }\FloatTok{2.5}\NormalTok{, }\FloatTok{3.0}\NormalTok{, }\FloatTok{3.5}\NormalTok{, }\FloatTok{4.0}\NormalTok{, }\FloatTok{4.5}\NormalTok{])}

\CommentTok{\# We define the notionals for the floating leg.}
\CommentTok{\# Note that we subtract 1 here because each floating notional }
\CommentTok{\# corresponds to an accrual period between two consecutive }
\CommentTok{\# payment dates in floating\_times}
\NormalTok{floating\_notionals }\OperatorTok{=} \FloatTok{10e6} \OperatorTok{*}\NormalTok{ np.ones(}\BuiltInTok{len}\NormalTok{(floating\_times) }\OperatorTok{{-}} \DecValTok{1}\NormalTok{)}

\CommentTok{\# We call the swap pricing function from the \textquotesingle{}fl\textquotesingle{} library provided }
\CommentTok{\# in Appendix}
\NormalTok{pv\_swap }\OperatorTok{=}\NormalTok{ fl.swap(}
\NormalTok{    df,  }\CommentTok{\# discount factor function}
\NormalTok{    fixed\_notionals,  }\CommentTok{\# notionals for fixed leg}
\NormalTok{    fixed\_times,  }\CommentTok{\# payment times for fixed leg}
\NormalTok{    fixed\_rates,  }\CommentTok{\# fixed rates per period}
\NormalTok{    floating\_notionals,  }\CommentTok{\# notionals for floating leg}
\NormalTok{    floating\_times,  }\CommentTok{\# payment times for floating leg}
\NormalTok{)}

\CommentTok{\# We print the present value of the swap}
\BuiltInTok{print}\NormalTok{(pv\_swap)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
3577.601351073943
\end{verbatim}

\subsubsection{European Swaption}\label{european-swaption}

We now focus on the Python implementation of pricing a European receiver
swaption using the Black model. Specifically, we price a European 1x5
receiver swaption using Bloomberg's pricer via the ``SWPM'' (``Swap
Manager'') function, and we aim to replicate this pricing in Python
using the Black model. Since we rely on the Black model to price a
European swaption, the volatility type used in the Bloomberg terminal is
lognormal.

This option gives the right, but not the obligation, to enter into an
interest rate swap starting on May 26, 2026, with a maturity of five
years. Both legs of the underlying swap have the same payment frequency,
the same day count convention (ACT/360) and the floating leg is indexed
to the overnight SOFR. The delivery type selected in Bloomberg is
``physical (cleared)'', meaning that if the option is exercised, the
investor will enter into the interest rate swap itself, rather than
receiving a cash settlement or the net present value of the swap at
expiry.

We discount the swap cash flows using OIS discounting, which is
consistent with the Bloomberg settings where the ``OIS DC Stripping''
option was selected. It's also important to note that Bloomberg provides
the par swap rate, which is the fixed rate that sets the net present
value of the underlying swap to zero at the option's expiry. It also
provides the implied lognormal volatility (in \%), which we use in the
Python code. The screenshot illustrating the Bloomberg pricing of such
swaption is provided below:

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Swaption/Swaption3.png}}

}

\caption{European Swaption Pricing (Source: Bloomberg Terminal)}

\end{figure}%

Once again, we provide below the code that defines the required inputs
and calls the swaption\_value function from the `fl' library. Note that
the implementation of this pricing function is included in the Appendix
for clarity and readability.

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\ImportTok{import}\NormalTok{ numpy }\ImportTok{as}\NormalTok{ np}
\ImportTok{import}\NormalTok{ finlib }\ImportTok{as}\NormalTok{ fl}
\end{Highlighting}
\end{Shaded}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\CommentTok{\# A boolean flag. False means it\textquotesingle{}s a receiver swaption.}
\CommentTok{\# True would mean it is a payer swaption}
\NormalTok{is\_payer }\OperatorTok{=} \VariableTok{False}

\CommentTok{\# Strike rate of the swaption (3.665375\%)}
\NormalTok{strike }\OperatorTok{=} \FloatTok{0.03665375}

\CommentTok{\# Notional amount of the underlying swap: 10 million}
\NormalTok{notional }\OperatorTok{=} \FloatTok{10e6}

\CommentTok{\# Payment times (in years) for the underlying swap}
\CommentTok{\# (starting in 1 year) cash flows}
\NormalTok{swap\_times }\OperatorTok{=}\NormalTok{ np.array([}\DecValTok{1}\NormalTok{, }\DecValTok{2}\NormalTok{, }\DecValTok{3}\NormalTok{, }\DecValTok{4}\NormalTok{, }\DecValTok{5}\NormalTok{, }\DecValTok{6}\NormalTok{])}

\CommentTok{\# The implied Black (lognormal) volatility of the}
\CommentTok{\# forward swap rate: 29.48\%}
\CommentTok{\# The below figure is provided by Bloomberg}
\NormalTok{vol }\OperatorTok{=} \FloatTok{0.2948}

\CommentTok{\# We call the swaption\_value function from the \textquotesingle{}fl\textquotesingle{} library}
\NormalTok{swap\_val }\OperatorTok{=}\NormalTok{ fl.swaption\_value(is\_payer, strike, notional,}
\NormalTok{                             swap\_times, df, vol)}

\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Net Present Value: }\SpecialCharTok{\{}\NormalTok{swap\_val}\SpecialCharTok{:,.2f\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Net Present Value: 176,067.45
\end{verbatim}

\subsubsection{Zero-coupon inflation
swap}\label{zero-coupon-inflation-swap}

We priced a five-year receiver zero-coupon inflation swap using
Bloomberg's pricer (via the SWPM function), in which the inflation leg
is received and the fixed leg is paid. The swap starts as of 05/23/2025
and ends as of 05/28/2030. The inflation index used is the CPURNSA,
which refers to the U.S. Consumer Price Index for All Urban Consumers,
Not Seasonally Adjusted. Bloomberg directly provides the base index
value for the zero-coupon inflation swap. This value represents the
reference CPI level at the start date of the swap and is used to
calculate the inflation accrual over the life of the contract. The day
count convention used in our pricing code, provided in the appendix, is
ACT/360. Note that a 3 month lag is applied, which is the standard
market convention for the U.S. inflation derivatives market.

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Inflation_swaps/zero_coupon_inflation_swap.png}}

}

\caption{Zero Coupon Inflation Swap Pricing (Source: Bloomberg
Terminal)}

\end{figure}%

Note that the ``OIS DC Stripping'' option has been checked, meaning we
assume the inflation derivative is collateralized. As a result, OIS
discounting is applied, and using SOFR-based discount factors (obtained
with our discount function) is appropriate.

The forward CPI mid values (the average between the ``CPI Bid'' and
``CPI Ask'' quotes), observed as of 05/23/2025, have been provided by
Bloomberg via the function ``SWPM'' (``Swap Manager''). However, because
there is a 3-month lag between the reference CPI and the settlement date
of the transaction, we cannot exactly take the forward CPI at exactly 5
years, even though the swap has a 5-year maturity. Furthermore, the
forward CPI values provided by Bloomberg are available only on an annual
basis. We needed the forward CPI level at 4.75 years (as of March 2030),
and to do so, we have to interpolate and start by defining, in the
Python code, the annual forward CPI curve retrieved from Bloomberg,
using a set of seven data points (using the first seven maturities and
values only, shown in the column ``Tenor'' and ``CPI Mid'',
respectively, in order to have enough information for the
interpolation). We then apply a quadratic interpolation on those forward
CPI mid values to estimate the implied CPI level 3-months before the
5-year point (again, as of March 2030).

By doing so, we obtain a more accurate projected CPI value as of March
2030, which allows us to replicate the Bloomberg price more precisely by
taking into account the inflation indexation lag. The relevant data
retrieved from Bloomberg are presented in the screenshot below. Note
that the screenshot also displays a plot of the zero-coupon inflation
curve (in green) across different tenors up to 30 years. Normally, the
components of this curve are obtained through a bootstrapping process.

\begin{figure}[H]

{\centering \pandocbounded{\includegraphics[keepaspectratio]{images/Inflation_swaps/Data2.png}}

}

\caption{Zero Coupon Inflation Curve and Forward CPI Mid values (Source:
Bloomberg Terminal)}

\end{figure}%

Furthermore, in this pricing case, the fixed inflation rate paid
(2.56\%, which corresponds to the ``ZC Mid'' value for a five-year tenor
swap) is slightly different from the breakeven inflation rate displayed
in the Bloomberg terminal pricer (shown in the ``Par Cpn'' field). This
difference explains why, at the initiation of the swap (as illustrated
in the bloomberg pricer screenshot), the net present value is not
approximately zero, but instead shows a value of \$-17,002.42.

We provide below an application of our
zero\_coupon\_inflation\_swap\_pricer function (shown in Appendix), with
the relevant input used. Some explanations in the code are also
provided.

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\ImportTok{import}\NormalTok{ datetime}

\NormalTok{tenors }\OperatorTok{=}\NormalTok{ np.arange(}\DecValTok{1}\NormalTok{, }\DecValTok{8}\NormalTok{)}

\CommentTok{\# We define the forward CPI values used for quadratic}
\CommentTok{\# interpolation}
\NormalTok{forward\_cpis }\OperatorTok{=}\NormalTok{ np.array([}\FloatTok{330.449}\NormalTok{, }\FloatTok{338.57}\NormalTok{, }\FloatTok{346.497}\NormalTok{, }\FloatTok{354.519}\NormalTok{,}
                         \FloatTok{362.844}\NormalTok{, }\FloatTok{371.441}\NormalTok{, }\FloatTok{380.321}\NormalTok{])}
\NormalTok{forward\_cpis\_interpolator }\OperatorTok{=}\NormalTok{ fl.flat\_quadratic\_interpolator(}
\NormalTok{    tenors,}
\NormalTok{    forward\_cpis}
\NormalTok{)}

\CommentTok{\# We set the current valuation date (trade date)}
\NormalTok{t0\_date }\OperatorTok{=}\NormalTok{ datetime.date(}\DecValTok{2025}\NormalTok{, }\DecValTok{5}\NormalTok{, }\DecValTok{23}\NormalTok{)}

\CommentTok{\# We define the start and maturity dates of the zero{-}coupon}
\CommentTok{\# inflation swap}
\NormalTok{start\_date }\OperatorTok{=}\NormalTok{ t0\_date}
\NormalTok{maturity\_date }\OperatorTok{=}\NormalTok{ datetime.date(}\DecValTok{2030}\NormalTok{, }\DecValTok{5}\NormalTok{, }\DecValTok{28}\NormalTok{)}

\CommentTok{\# Notional amount of the swap}
\NormalTok{notional }\OperatorTok{=} \DecValTok{10\_000\_000}

\CommentTok{\# Fixed rate to be received}
\NormalTok{fixed\_rate }\OperatorTok{=} \FloatTok{0.0256}

\CommentTok{\# Current (observed) CPI index at trade date}
\NormalTok{cpi\_t0 }\OperatorTok{=} \FloatTok{319.082}

\NormalTok{default\_lag }\OperatorTok{=} \DecValTok{1}

\CommentTok{\# actual market convention lag used in U.S. inflation swaps}
\NormalTok{trade\_lag }\OperatorTok{=} \DecValTok{3}

\CommentTok{\# We calculate the number of months between today and maturity,}
\CommentTok{\# adjusted for lag}
\NormalTok{diff\_monts }\OperatorTok{=}\NormalTok{ (fl.diff\_months(t0\_date, maturity\_date)}
              \OperatorTok{{-}}\NormalTok{ (trade\_lag }\OperatorTok{{-}}\NormalTok{ default\_lag))}

\CommentTok{\# Estimate the forward CPI at maturity by interpolating on}
\CommentTok{\# the forward CPI curve}
\NormalTok{cpi\_forward }\OperatorTok{=}\NormalTok{ forward\_cpis\_interpolator([diff\_monts }\OperatorTok{/} \FloatTok{12.0}\NormalTok{])[}\DecValTok{0}\NormalTok{]}

\NormalTok{cpi\_forward\_next\_month }\OperatorTok{=} \VariableTok{None}
\NormalTok{linear\_indexation }\OperatorTok{=} \VariableTok{False}

\CommentTok{\# Call the pricer function to compute the Net Present Value}
\CommentTok{\# of the ZCIS}
\NormalTok{npv }\OperatorTok{=}\NormalTok{ fl.zero\_coupon\_inflation\_swap\_pricer(}
\NormalTok{    t0\_date,}
\NormalTok{    start\_date,}
\NormalTok{    maturity\_date,}
\NormalTok{    notional,}
\NormalTok{    fixed\_rate,}
\NormalTok{    cpi\_t0,}
\NormalTok{    cpi\_forward,}
\NormalTok{    cpi\_forward\_next\_month,}
\NormalTok{    df,}
\NormalTok{    linear\_indexation,}
\NormalTok{)}

\CommentTok{\# Print the NPV result, formatted with comma separators}
\CommentTok{\# and two digits after the decimal point}
\BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Net Present Value (Zero{-}coupon Inflation Swap): "}
      \SpecialStringTok{f"}\SpecialCharTok{\{}\NormalTok{npv}\SpecialCharTok{:,.2f\}}\SpecialStringTok{"}\NormalTok{)}
\end{Highlighting}
\end{Shaded}

\begin{verbatim}
Net Present Value (Zero-coupon Inflation Swap): -16,455.96
\end{verbatim}

\subsection{Discussion of results}\label{discussion-of-results}

The implementation of pricing models for the in-scope financial
instruments has provided insights into the practical aspects of
financial pricing. This section presents the key findings from our
Python implementations and compares them with Bloomberg market prices.

\subsubsection{Stock Valuation Results}\label{stock-valuation-results}

Our implementation aimed to illustrate the enterprise DCF methodology by
applying free cash flow projections, terminal value estimation, and the
weighted average cost of capital (WACC). However, several limitations
arose during the implementation process. Our discounted cash flow (DCF)
valuation of Apple Inc.~resulted in an estimated share price of \$173.72
that appears to be undervalued relative to the observed market price of
\$195.27 as of May 23, 2025, as we probably not fully captured the full
value of non-operating assets in our model.

We assumed a constant 8\% annual growth in free cash flows over the
five-year explicit forecast period, which simplifies the underlying
economic reality. Indeed, in practice, free cash flows to the firm do
not grow at a linear constant rate but rather fluctuate with new product
launches, competitive dynamics, and macroeconomic conditions. This
linear growth assumption therefore introduces bias into our valuation,
as it fails to reflect the real-world volatility of Apple's operational
performance. A more sophisticated approach would incorporate scenario
analysis based on different growth rate assumptions, for example, using
a base case and a best case scenario. Furthermore, it is also necessary
to forecast over a longer explicit forecast period in order to compare
both results obtained, as it is well known that for shorter explicit
valuation period, the terminal value tends to carry a higher weight in
the overall enterprise value.

We believe that the terminal growth rate of 4\% used in our model is
still a reasonable and acceptable assumption, given Apple's strong
brand, global presence, and innovation capabilities, even though this
value might appear optimistic for a mature company like Apple. However,
we suspect that the WACC used in our valuation may be slightly
overestimated, which could have contributed to the lower estimated share
price.

Note also that for the stock valuation, we relied on additional data
sources such as market websites, FactSet, and Apple's most recent
consolidated financial statements.

\subsubsection{Options Pricing Analysis}\label{options-pricing-analysis}

We have been satisfied with our implementation of the Black-Scholes
pricing formulas for European call and put options on Apple stock. The
prices of both call and put options (AAPL 01/16/26 C205 and AAPL
12/19/25 P205) obtained using Python are close to the last traded market
prices shown on Bloomberg.

Our pricing incorporated dividend adjustments by converting discrete
dividends into a continuous dividend yield. The Newton's method
implementation for implied volatility calculation has been helpful, as
it recovered the Bloomberg-provided implied volatility of 30.42\% for
AAPL 01/16/26 C205, confirming the accuracy of our Black-Scholes
implementation.

In addition, the calculation and replication of the option Greeks
(Delta, Gamma, Vega, and Theta) showed acceptable alignment with
Bloomberg's pricer values. We also made, in Python, specific adjustments
to the computation of the Greeks in order to align with Bloomberg's
market conventions, ensuring a more accurate replication. Indeed,
Bloomberg quotes, for example, Theta as a decay per calendar days (not
per trading day) so we divided the Theta value from the code by 365.
Also, it quotes also Vega per 1\% change in implied volatility so we had
to divide the result from the code by 100 to match the convention.

\subsubsection{Bond Pricing and Bootstrapping
Methodology}\label{bond-pricing-and-bootstrapping-methodology}

Our calculated clean price provided a reasonable approximation of the
Bloomberg quoted price of 110.3125. The use of SOFR-based discount
factors has been an appropriate choice for pricing a U.S. Treasury
security, as SOFR is considered by the market to be an acceptable proxy
for the risk-free rate.

Furthermore, the bootstrapping of zero-coupon rates from SOFR swap rates
and our discount function represented key components of our fixed income
pricing infrastructure. The resulting zero-rate curve is consistent with
the Bloomberg zero curve shown in this paper. The quadratic
interpolation method ensures a smooth and stable transitions across
tenors, although other methods such as cubic spline interpolation or
piecewise linear interpolation. Note that we omitted SOFR swap rates
with tenors shorter than 12 months to reduce short-term volatility and
therefore focus on long-term expectations. When we incorporate those
short-term maturities, the discrepancy between Python and Bloomberg
results, particularly for interest rate swaps, becomes larger.

Note that using a single OIS discount curve may not be appropriate in
cases such as corporate bond pricing, where zero rates must be adjusted
with a credit spread to reflect the issuer's credit quality and default
risk. Furthermore, for pricing an inflation swap, one could use
inflation discount factors bootstrapped from zero-coupon inflation swap
breakeven rates, which help construct a term structure of real
zero-coupon rates. In our case, we used the forward implied CPI Mid
values provided by Bloomberg.

\subsubsection{Derivatives Pricing
Challenges}\label{derivatives-pricing-challenges}

Our 4.5-year USD SOFR receiver swap pricing resulted in an almost zero
net present value (\$3,577.60), which confirms the accuracy of the par
fixed rate provided by Bloomberg. Furthermore, the use of OIS
discounting reflects current market practices for collateralized
derivatives (as CSA agreements specify that collateral must be
remunerated at an overnight rate), highlighting the importance, since
the 2008 financial crisis, of taking into account funding and collateral
considerations into derivatives pricing models. Using the correct
discount factors was important to replicate market prices, as they are
one of the most sensitive inputs in the model.

The European swaption pricing using the Black model was another
challenge. Our Python pricing code yielded an undervalued price
(\$176,067.45) in comparison with Bloomberg market price (\$189,018.77),
probably due to differences in the zero rates and discount factors
applied to the valuation of the underlying swap. We made sure to select
the ``OIS DC Stripping'' option to ensure consistency in the discounting
methodology between Python and Bloomberg. We relied on the constant
implied volatility value provided by Bloomberg in our Python
implementation. However, in practice, swaption implied volatilities vary
as a function of the option expiry (how long until the option can be
exercised) and the tenor of the underlying swap (the length of the
interest rate swap that would start at expiry if the option is
exercised).

The zero-coupon inflation swap pricing was one of the most complex
implementations, because it required considering inflation indexation
lags. We managed to stay close to the Bloomberg observed price of
--\$17,002.42, with our model producing a result of --\$16,455.96. The
3-month lag market convention and the need for quadratic interpolation
of annual forward CPI data highlighted the practical challenges involved
in pricing inflation derivatives. The negative NPV of --\$17,002.42
arose because the fixed inflation rate used in the swap (2.56\%) was
slightly higher than the market breakeven rate provided by Bloomberg
(2.523725\%). When the latter is used, the swap prices fairly, yielding
a net present value of \$177.63. However, note that we used an Act/360
day count convention, both in the Bloomberg pricer and in our code,
whereas the ISDA standard convention for U.S. inflation swaps is Act/Act
(ISDA).

\subsubsection{Practical Implementation
Insights}\label{practical-implementation-insights}

We implemented the pricing code using Jupyter Notebooks. We also used
Quarto, which is a publishing system that enabled us to write formulas
and create a structured report in an academic format. Furthemore, as we
had access to Bloomberg, it was naturally an appropriate source of
market data for this paper, given its reliability for academic work.
Writing this paper also helped us further improve our Python skills,
explore the necessary libraries, and learn how to use a professional
data interface such as Bloomberg. Note that Credit Default Swaps (CDS)
and interest rate Caps were initially intended to be part of the scope
of this paper. However, due to time constraints and the necessity of
meeting the project deadline, we decided to exclude them from the final
scope.

At each step of the code implementation for each instrument, we called
the relevant pricing function and specified its required inputs, either
taken from Bloomberg or from other sources such as Yahoo Finance or
Apple's 2024 financial statements for stock valuation. Providing the
source code in a dedicated section at the end of the report helped
maintain a clean and readable presentation of the results, without
overwhelming it with implementation details. In the results section, we
added comments to the code to help readers better understand the
implementation and logic behind each step.

The comparison with Bloomberg prices was an interesting mechanism, even
though perfect replication was not achieved due to differences in zero
rates, discount factors used in comparison with Bloomberg, and potential
non-public adjustments that Bloomberg may incorporate. The most
sensitive inputs impacting price replication are the discount factors,
where even slight differences can widen the gap between Python and
Bloomberg prices. To reduce differences with Bloomberg as much as
possible, we extracted the SOFR swap rates on the same day the
instruments were priced in Bloomberg. We could have used the same zero
rates and discount factors as Bloomberg, but we considered it was more
interesting to rely on our own outputs, based on our bootstrap code and
discount function.

\section{Conclusion}\label{conclusion}

This report aimed to present key theoretical pricing models and
concepts, while also offering an overview of how such theories can be
implemented in practice using coding tools like Python. Bloomberg served
as a reliable benchmark, helping us validate our results and gain
confidence in the accuracy of our Python implementation. The practical
section of this paper highlights that attention to detail and careful
justification of input data are essential for producing reliable and
consistent pricing outcomes. We invite the reader to explore additional
theoretical resources that cover more advanced pricing methods, such as
those based on stochastic processes.

\section{Bibliography}\label{bibliography}

\phantomsection\label{refs}
\begin{CSLReferences}{1}{0}
\bibitem[\citeproctext]{ref-ARRC2019}
Alternative Reference Rates Committee. (2019). \emph{A user's guide to
SOFR}.

\bibitem[\citeproctext]{ref-ARRC2021}
Alternative Reference Rates Committee. (2021). \emph{An updated user's
guide to SOFR}. Alternative Reference Rates Committee.

\bibitem[\citeproctext]{ref-Andersen2010}
Andersen, L. B. G., \& Piterbarg, V. V. (2010). \emph{Interest rate
modeling: Vol. I: Foundations and vanilla models; vol. II: Term
structure models; vol. III: Products and risk}. Atlantic Financial
Press.

\bibitem[\citeproctext]{ref-Bacha2023}
Bacha, O. I. (2023). \emph{Financial derivatives: Markets and
applications}. World Scientific.

\bibitem[\citeproctext]{ref-Bachelier1900}
Bachelier, L. (1900). Théorie de la spéculation. \emph{Annales
Scientifiques de l'École Normale Supérieure}, \emph{17}, 21--86.

\bibitem[\citeproctext]{ref-Bernhart2013}
Bernhart, G. (2013). \emph{Interest rate bootstrapping explained}. XAIA
Investment GmbH.
\url{https://www.xaia.com/fileadmin/user_upload/media_migrated/Bootstrap_XAIA_02.pdf}

\bibitem[\citeproctext]{ref-BjerksundStensland1993}
Bjerksund, P., \& Stensland, G. (1993). Closed-form approximation of
american options. \emph{Scandinavian Journal of Management}, \emph{9},
S87--S99.

\bibitem[\citeproctext]{ref-BjerksundStensland2002}
Bjerksund, P., \& Stensland, G. (2002). \emph{Closed-form approximation
of american options}.

\bibitem[\citeproctext]{ref-BlackScholes1973}
Black, F., \& Scholes, M. (1973). The pricing of options and corporate
liabilities. \emph{The Journal of Political Economy}.

\bibitem[\citeproctext]{ref-BlackRock2024}
BlackRock. (2024). \emph{What is an ETF?}
\url{https://www.ishares.com/us/investor-education/etf-education/what-is-an-etf}

\bibitem[\citeproctext]{ref-Boness1964}
Boness, A. J. (1964). Elements of a theory of stock-option value.
\emph{Journal of Political Economy}, \emph{72}(2), 163--175.

\bibitem[\citeproctext]{ref-Bouzoubaa2010}
Bouzoubaa, M., \& Osseiran, A. (2010). \emph{Exotic options and hybrids:
A guide to structuring, pricing and trading}. John Wiley \& Sons.

\bibitem[\citeproctext]{ref-Boyle2019}
Boyle, P., \& McDougall, J. (2019). \emph{Trading and pricing financial
derivatives: A guide to futures, options, and swaps}. De Gruyter.

\bibitem[\citeproctext]{ref-Chan2024}
Chan, R. H., Guo, Y. ZY., Lee, S. T., \& Li, X. (2024). \emph{Financial
mathematics, derivatives and structured products}. Springer.

\bibitem[\citeproctext]{ref-Choudhry2005}
Choudhry, M. (2005). \emph{Fixed income securities and derivatives
handbook}. Bloomberg Press.

\bibitem[\citeproctext]{ref-Choudhry2010}
Choudhry, M. (2010). \emph{An introduction to bond markets}. Wiley.

\bibitem[\citeproctext]{ref-CME2019}
CME Group. (2019). \emph{CME SOFR futures -- product overview}. CME
Group.
\url{https://www.cmegroup.com/trading/interest-rates/files/sofr-futures-product-overview.pdf}

\bibitem[\citeproctext]{ref-CGFS2017}
Committee on the Global Financial System. (2017). \emph{Repo market
functioning} {[}CGFS Papers{]}. Bank for International Settlements.

\bibitem[\citeproctext]{ref-Corb2012}
Corb, H. (2012). \emph{Interest rate swaps and other derivatives}.
Columbia Business School Publishing.

\bibitem[\citeproctext]{ref-Cox1979}
Cox, J. C., Ross, S. A., \& Rubinstein, M. (1979). Option pricing: A
simplified approach. \emph{Journal of Financial Economics}.

\bibitem[\citeproctext]{ref-Damodaran2025}
Damodaran, A. (2025). \emph{Valuation: Lecture note packet 1 ---
intrinsic valuation}. New York University, Stern School of Business.

\bibitem[\citeproctext]{ref-Dash2021}
Dash, J. W. (2021). \emph{Quantitative finance and risk management: A
physicist's approach}. World Scientific.

\bibitem[\citeproctext]{ref-DeutscheBank2021}
Deutsche Bank. (2021). \emph{A guide to IBOR transition}.
\url{https://corporates.db.com/files/documents/gated-content/DB_Guide-to-IBOR-Transition_40pp_Web_Secured.pdf}

\bibitem[\citeproctext]{ref-Edwards2024}
Edwards, S., Fulton, B., Helsing, D., \& Degis, A. P. (2024). \emph{An
introduction to fixed income}. Morgan Stanley Global Investment Office.
\url{https://advisor.morganstanley.com/norbert.frassa/documents/field/n/no/norbert-frassa/An_Introduction_to_Fixed_Income_\%28Bonds\%29.pdf}

\bibitem[\citeproctext]{ref-Fabozzi2021}
Fabozzi, F. J. (2021). \emph{The handbook of fixed income securities}.
McGraw Hill.

\bibitem[\citeproctext]{ref-FidelityInvestments2017}
Fidelity Investments. (2017). \emph{Understanding bond pricing}.
\url{https://www.fidelity.com/bin-public/060_www_fidelity_com/documents/learning-center/Bonds_Slidesv3a.pdf}

\bibitem[\citeproctext]{ref-GlobalX2023}
Global X ETFs. (2023). \emph{ETF NAV, iNAV \& price, explained}.
\url{https://globalxetfs.eu/content/files/ETF-NAV-iNAV-and-Price-Explained.pdf}

\bibitem[\citeproctext]{ref-Graber2009}
Gräber, P.-W. (2009). \emph{Interpolation methods}.

\bibitem[\citeproctext]{ref-Gregory2020}
Gregory, J. (2020). \emph{The xVA challenge: Counterparty risk, funding,
collateral, capital, and initial margin}. Wiley.

\bibitem[\citeproctext]{ref-HaganWest2006}
Hagan, P. S., \& West, G. (2006). Interpolation methods for curve
construction. \emph{Applied Mathematical Finance}.

\bibitem[\citeproctext]{ref-HaganWest2008}
Hagan, P. S., \& West, G. (2008). Methods for constructing a yield
curve. \emph{Wilmott Magazine}.

\bibitem[\citeproctext]{ref-Hamada1972}
Hamada, R. S. (1972). The effect of the firm's capital structure on the
systematic risk of common stocks. \emph{The Journal of Finance}.

\bibitem[\citeproctext]{ref-HarrisMarston2001}
Harris, R. S., \& Marston, F. C. (2001). The market risk premium:
Expectational estimates using analysts' forecasts. \emph{Financial
Management}.
\url{https://www.floridapsc.com/library/filings/2016/07490-2016/Support/19a_Harris\%20Marston_2001_Market\%20Risk\%20Premium\%20Expectational\%20Estimates.pdf}

\bibitem[\citeproctext]{ref-Haug2007}
Haug, E. G. (2007). \emph{The complete guide to option pricing
formulas}. McGraw-Hill.

\bibitem[\citeproctext]{ref-Hoover2005}
Hoover, S. (2005). \emph{Stock valuation: An essential guide to wall
street's most popular valuation models}. McGraw-Hill.

\bibitem[\citeproctext]{ref-Hull2022}
Hull, J. C. (2022). \emph{Options, futures, and other derivatives}.
Pearson.

\bibitem[\citeproctext]{ref-ICE2025}
Intercontinental Exchange. (2025). \emph{Three things to watch in ETFs
in 2025}.
\url{https://www.ice.com/insights/indices/three-things-to-watch-in-etfs-in-2025}

\bibitem[\citeproctext]{ref-ISDA2024}
International Swaps and Derivatives Association. (2024). \emph{SwapsInfo
full year 2023 and the fourth quarter of 2023 review}. International
Swaps; Derivatives Association.

\bibitem[\citeproctext]{ref-James2023}
James, J., \& Webber, N. (2023). \emph{Inflation derivatives:
Introduction, pricing and risk management}. Palgrave Macmillan.

\bibitem[\citeproctext]{ref-Jobair2021}
Jobair, H. K. (2021). \emph{Chapter three: interpolation}. University of
Baghdad.

\bibitem[\citeproctext]{ref-JPMorgan2023}
J.P. Morgan Asset Management. (2023). \emph{From evolution to
revolution: The power of active fixed income ETFs}.
\url{https://am.jpmorgan.com/content/dam/jpm-am-aem/global/en/insights/etf-insights/the-power-of-active-fixed-income-etfs.pdf}

\bibitem[\citeproctext]{ref-Kolb2006}
Kolb, R. W., \& Overdahl, J. A. (2006). \emph{Understanding futures
markets} (6th ed.). Blackwell Publishing.

\bibitem[\citeproctext]{ref-Koller2010}
Koller, T., Goedhart, M., \& Wessels, D. (2010). \emph{Valuation:
Measuring and managing the value of companies}. John Wiley \& Sons.

\bibitem[\citeproctext]{ref-Lin2016}
Lin, Y., \& Yang, J. (2016). Option pricing model based on
newton-raphson iteration and RBF neural network using implied
volatility. \emph{Canadian Social Science}.

\bibitem[\citeproctext]{ref-Liffe2002}
London International Financial Futures and Options Exchange. (2002).
\emph{LIFFE options: A guide to trading strategies}.

\bibitem[\citeproctext]{ref-Branka2014}
Marasović, B., Aljinović, Z., \& Pečoković, T. (2014). Numerical methods
versus bjerskund and stensland approximations for american options
pricing. \emph{International Journal of Economics and Management
Engineering}.

\bibitem[\citeproctext]{ref-Merton1973}
Merton, R. C. (1973). Theory of rational option pricing. \emph{The Bell
Journal of Economics and Management Science}.

\bibitem[\citeproctext]{ref-Modigliani1958}
Modigliani, F., \& Miller, M. H. (1958). The cost of capital,
corporation finance and the theory of investment. \emph{The American
Economic Review}.

\bibitem[\citeproctext]{ref-Murray2010}
Murray, W. (2010). \emph{Newton-type methods}. Department of Management
Science; Engineering, Stanford University.

\bibitem[\citeproctext]{ref-Nawalkha2005}
Nawalkha, S. K., Soto, G. M., \& Beliaeva, N. A. (2005). \emph{Interest
rate risk modeling: The fixed income valuation course}. Wiley.

\bibitem[\citeproctext]{ref-OesterreichischeNationalBank2004}
Oesterreichische Nationalbank. (2004). \emph{Guidelines on interest rate
risk management}. Oesterreichische Nationalbank.

\bibitem[\citeproctext]{ref-Parameswaran2022}
Parameswaran, S. K. (2022). \emph{Fundamentals of financial instruments:
An introduction to stocks, bonds, foreign exchange, and derivatives}.
Wiley.

\bibitem[\citeproctext]{ref-Pelsser2000}
Pelsser, A. (2000). \emph{Efficient methods for valuing interest rate
derivatives}. Springer-Verlag.

\bibitem[\citeproctext]{ref-Poncet2022}
Poncet, P., \& Portait, R. (2022). \emph{Capital market finance: An
introduction to primitive assets, derivatives, portfolio management and
risk}. Springer.

\bibitem[\citeproctext]{ref-Privault2022}
Privault, N. (2022). \emph{Introduction to stochastic finance with
market examples}. Chapman \& Hall/CRC.

\bibitem[\citeproctext]{ref-Ramirez2015}
Ramirez, J. (2015). \emph{Accounting for derivatives: Advanced hedging
under IFRS 9}. Wiley.

\bibitem[\citeproctext]{ref-Rendleman1979}
Rendleman, R. J., \& Bartter, B. J. (1979). Two-state option pricing.
\emph{Journal of Finance}.

\bibitem[\citeproctext]{ref-Richards2022}
Richards, P. (2022). The transition of legacy US dollar LIBOR bonds
under english law. \emph{ICMA Quarterly Report}.
\url{https://www.icmagroup.org/assets/Paul-Richards_ICMA-Quarterly-Report-Q4-2022.pdf}

\bibitem[\citeproctext]{ref-Samuelson1965}
Samuelson, P. A. (1965). Rational theory of warrant pricing.
\emph{Industrial Management Review}, \emph{6}(2), 13--31.

\bibitem[\citeproctext]{ref-Schofield2011}
Schofield, N. C., \& Bowler, T. (2011). \emph{Trading the fixed income,
inflation, and credit markets: A relative value guide}. Wiley.

\bibitem[\citeproctext]{ref-SIFMA2024}
Securities Industry and Financial Markets Association. (2024).
\emph{Primer: Global equity markets comparison -- comparing market
volumes, capitalization, formation, performance, \& costs}. Securities
Industry; Financial Markets Association.

\bibitem[\citeproctext]{ref-SIFMA2025}
Securities Industry and Financial Markets Association. (2025).
\emph{2025 capital markets outlook}. Securities Industry; Financial
Markets Association.

\bibitem[\citeproctext]{ref-Murugesan2019}
Selvam, M. (2019). \emph{Stock market analysis and investment
strategies}. Academic Press.

\bibitem[\citeproctext]{ref-Sprenkle1961}
Sprenkle, C. M. (1961). Warrant prices as indicators of expectations and
preferences. \emph{Yale Economic Essays}, \emph{1}, 178--231.

\bibitem[\citeproctext]{ref-StateStreet2020}
State Street Global Advisors. (2020). \emph{ETF liquidity: Master the
mechanics of ETF trading}.
\url{https://www.ssga.com/library-content/pdfs/etf/au/spdr-au-etf-liquidity-master-the-mechanics-of-etf-trading.pdf}

\bibitem[\citeproctext]{ref-Tan2010}
Tan, C. (2010). \emph{Demystifying exotic products: Interest rates,
equities and foreign exchange}. Wiley.

\bibitem[\citeproctext]{ref-InvestmentAssociation2018}
The Investment Association. (2018). \emph{ETFs: A beginner's guide}.
\url{https://www.theia.org/sites/default/files/2019-04/20181129-etfsabeginnersguide.pdf}

\bibitem[\citeproctext]{ref-Tuckman2022}
Tuckman, B., \& Serrat, A. (2022). \emph{Fixed income securities: Tools
for today's markets}. Wiley.

\bibitem[\citeproctext]{ref-Vandeboogert2017}
Vandeboogert, K. (2017). \emph{Method of quadratic interpolation}.
University of South Carolina.

\bibitem[\citeproctext]{ref-Vanguard2023}
Vanguard. (2023). \emph{Understanding the total cost of ETF ownership}.
\url{https://corporate.vanguard.com/content/dam/corp/articles/pdf/understanding_the_total_cost_of_etf_ownership.pdf}

\bibitem[\citeproctext]{ref-WisdomTree2018}
WisdomTree Europe. (2018). \emph{Intra-day pricing: How ETF shares are
priced}.
\url{https://www.wisdomtree.eu/-/media/eu-media-files/other-documents/educational/intra-day-pricing-how-etf-shares-are-priced.pdf}

\bibitem[\citeproctext]{ref-Wu2022}
Wu, L. (2022). \emph{Interest rate modeling: Theory and practice}.
Chapman \& Hall/CRC.

\bibitem[\citeproctext]{ref-Yen2015}
Yen, M. (2015). \emph{Options trading: Strategies and techniques for
success}. Financial Press.

\bibitem[\citeproctext]{ref-ZhouAbramov2019}
Zhou, X., \& Abramov, V. (2019). \emph{A practical guide to interest
rate curve building validations (w/ excel replica of bloomberg libor \&
GitHub)}. SSRN.

\end{CSLReferences}

\section{Appendix}\label{appendix}

\subsection{Source Code}\label{source-code}

\subsubsection{\_\_init\_\_.py}\label{init__.py}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\ImportTok{from}\NormalTok{ .pricing }\ImportTok{import} \OperatorTok{*}
\ImportTok{from}\NormalTok{ .vanilla }\ImportTok{import} \OperatorTok{*}
\ImportTok{from}\NormalTok{ .rates }\ImportTok{import} \OperatorTok{*}
\end{Highlighting}
\end{Shaded}

\subsubsection{pricing.py}\label{pricing.py}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\ImportTok{import}\NormalTok{ numpy }\ImportTok{as}\NormalTok{ np}



\CommentTok{\# Enterprise Value}

\KeywordTok{def}\NormalTok{ enterprise\_value(}
\NormalTok{    fcff\_initial, growth\_rates, wacc,}
\NormalTok{        terminal\_growth\_rate, non\_operating\_assets}
\NormalTok{):}
\NormalTok{    n }\OperatorTok{=} \BuiltInTok{len}\NormalTok{(growth\_rates)}
\NormalTok{    fcff }\OperatorTok{=}\NormalTok{ [fcff\_initial }\OperatorTok{*}\NormalTok{ (}\DecValTok{1} \OperatorTok{+}\NormalTok{ growth\_rates[}\DecValTok{0}\NormalTok{])]}

    \ControlFlowTok{for}\NormalTok{ i }\KeywordTok{in} \BuiltInTok{range}\NormalTok{(}\DecValTok{1}\NormalTok{, n):}
\NormalTok{        fcff.append(fcff[i }\OperatorTok{{-}} \DecValTok{1}\NormalTok{] }\OperatorTok{*}\NormalTok{ (}\DecValTok{1} \OperatorTok{+}\NormalTok{ growth\_rates[i]))}

\NormalTok{    discounted\_fcff }\OperatorTok{=}\NormalTok{ [fcff[i] }\OperatorTok{/}\NormalTok{ ((}\DecValTok{1} \OperatorTok{+}\NormalTok{ wacc) }\OperatorTok{**}\NormalTok{ (i }\OperatorTok{+} \DecValTok{1}\NormalTok{))}
                       \ControlFlowTok{for}\NormalTok{ i }\KeywordTok{in} \BuiltInTok{range}\NormalTok{(n)]}
\NormalTok{    terminal\_fcff }\OperatorTok{=}\NormalTok{ fcff[}\OperatorTok{{-}}\DecValTok{1}\NormalTok{] }\OperatorTok{*}\NormalTok{ (}\DecValTok{1} \OperatorTok{+}\NormalTok{ terminal\_growth\_rate)}
\NormalTok{    terminal\_value }\OperatorTok{=}\NormalTok{ terminal\_fcff }\OperatorTok{/}\NormalTok{ (wacc }\OperatorTok{{-}}\NormalTok{ terminal\_growth\_rate)}
\NormalTok{    discounted\_terminal\_value }\OperatorTok{=}\NormalTok{ terminal\_value }\OperatorTok{/}\NormalTok{ ((}\DecValTok{1} \OperatorTok{+}\NormalTok{ wacc) }\OperatorTok{**}\NormalTok{ n)}

    \ControlFlowTok{return}\NormalTok{ (}\BuiltInTok{sum}\NormalTok{(discounted\_fcff) }\OperatorTok{+}\NormalTok{ discounted\_terminal\_value}
            \OperatorTok{+}\NormalTok{ non\_operating\_assets)}

\CommentTok{\# Equity Value}

\KeywordTok{def}\NormalTok{ equity\_value(enterprise\_value, non\_equity\_liabilities):}
\NormalTok{    equity\_value }\OperatorTok{=}\NormalTok{ enterprise\_value }\OperatorTok{{-}}\NormalTok{ non\_equity\_liabilities}

    \ControlFlowTok{return}\NormalTok{ equity\_value}
\end{Highlighting}
\end{Shaded}

\subsubsection{rates.py}\label{rates.py}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\ImportTok{import}\NormalTok{ numpy }\ImportTok{as}\NormalTok{ np}
\ImportTok{from}\NormalTok{ scipy.interpolate }\ImportTok{import}\NormalTok{ interp1d}
\ImportTok{from}\NormalTok{ datetime }\ImportTok{import}\NormalTok{ date}
\ImportTok{import}\NormalTok{ calendar}

\ImportTok{from}\NormalTok{ .vanilla }\ImportTok{import}\NormalTok{ gblack\_scholes}



\CommentTok{\# IRS Pricing}

\KeywordTok{def}\NormalTok{ swap(}
\NormalTok{    df,}
\NormalTok{    fixed\_notionals,}
\NormalTok{    fixed\_times,}
\NormalTok{    fixed\_rates,}
\NormalTok{    floating\_notionals,}
\NormalTok{    floating\_times,}
\NormalTok{):}
\NormalTok{    fixed\_dcfs }\OperatorTok{=}\NormalTok{ fixed\_times[}\DecValTok{1}\NormalTok{:] }\OperatorTok{{-}}\NormalTok{ fixed\_times[:}\OperatorTok{{-}}\DecValTok{1}\NormalTok{]}
\NormalTok{    fixed\_dfs }\OperatorTok{=}\NormalTok{ np.array([df(end\_time)}
                          \ControlFlowTok{for}\NormalTok{ end\_time }\KeywordTok{in}\NormalTok{ fixed\_times[}\DecValTok{1}\NormalTok{:]])}
\NormalTok{    fixed\_leg }\OperatorTok{=}\NormalTok{ (fixed\_notionals}
                 \OperatorTok{*}\NormalTok{ fixed\_dcfs}
                 \OperatorTok{*}\NormalTok{ fixed\_rates}
                 \OperatorTok{*}\NormalTok{ fixed\_dfs).}\BuiltInTok{sum}\NormalTok{()}

\NormalTok{    floating\_starts }\OperatorTok{=}\NormalTok{ floating\_times[:}\OperatorTok{{-}}\DecValTok{1}\NormalTok{]}
\NormalTok{    floating\_ends }\OperatorTok{=}\NormalTok{ floating\_times[}\DecValTok{1}\NormalTok{:]}
\NormalTok{    floating\_dcfs }\OperatorTok{=}\NormalTok{ floating\_ends }\OperatorTok{{-}}\NormalTok{ floating\_starts}

\NormalTok{    floating\_dfs }\OperatorTok{=}\NormalTok{ np.array([df(end\_time)}
                             \ControlFlowTok{for}\NormalTok{ end\_time }\KeywordTok{in}\NormalTok{ floating\_ends])}
\NormalTok{    libor\_rates }\OperatorTok{=}\NormalTok{ np.array(}
\NormalTok{        [}
\NormalTok{            linear\_rate(start\_time, end\_time, df)}
            \ControlFlowTok{for}\NormalTok{ start\_time, end\_time}
            \KeywordTok{in} \BuiltInTok{zip}\NormalTok{(floating\_starts, floating\_ends)}
\NormalTok{        ]}
\NormalTok{    )}
\NormalTok{    floating\_leg }\OperatorTok{=}\NormalTok{ (}
\NormalTok{        floating\_notionals}
        \OperatorTok{*}\NormalTok{ floating\_dcfs}
        \OperatorTok{*}\NormalTok{ libor\_rates}
        \OperatorTok{*}\NormalTok{ floating\_dfs}
\NormalTok{    ).}\BuiltInTok{sum}\NormalTok{()}

    \ControlFlowTok{return}\NormalTok{ fixed\_leg }\OperatorTok{{-}}\NormalTok{ floating\_leg}


\CommentTok{\# Bond pricing}

\KeywordTok{def}\NormalTok{ bond\_price(df, notionals, coupon\_times, fixed\_rates):}
\NormalTok{    discount\_factors }\OperatorTok{=}\NormalTok{ np.array([df(T) }\ControlFlowTok{for}\NormalTok{ T }\KeywordTok{in}\NormalTok{ coupon\_times])}

    \ControlFlowTok{return}\NormalTok{ np.}\BuiltInTok{sum}\NormalTok{(notionals }\OperatorTok{*}\NormalTok{ fixed\_rates }\OperatorTok{*}\NormalTok{ discount\_factors)}


\KeywordTok{def}\NormalTok{ linear\_rate(start, end, df, dcf}\OperatorTok{=}\VariableTok{None}\NormalTok{):}
    \CommentTok{\# df(end) = df(start) * (1 / (1 + R(start, end)}
    \CommentTok{\#           * dcf(start, end))}
    \CommentTok{\# dcf = day count fraction}
\NormalTok{    df\_start }\OperatorTok{=}\NormalTok{ df(start)}
\NormalTok{    df\_end }\OperatorTok{=}\NormalTok{ df(end)}
\NormalTok{    used\_dcf }\OperatorTok{=}\NormalTok{ dcf }\ControlFlowTok{if}\NormalTok{ dcf }\KeywordTok{is} \KeywordTok{not} \VariableTok{None} \ControlFlowTok{else}\NormalTok{ (end }\OperatorTok{{-}}\NormalTok{ start)}

    \ControlFlowTok{return}\NormalTok{ (df\_start }\OperatorTok{/}\NormalTok{ df\_end }\OperatorTok{{-}} \FloatTok{1.0}\NormalTok{) }\OperatorTok{/}\NormalTok{ used\_dcf}


\CommentTok{\# Swaption Pricing    }

\KeywordTok{def}\NormalTok{ swaption\_value(is\_payer, strike, notional, swap\_times,}
\NormalTok{                   df, vol):}
    \CommentTok{\# positive notional means long option,}
    \CommentTok{\# is\_payer is used for the swap direction}
\NormalTok{    swap\_rate, annuity }\OperatorTok{=}\NormalTok{ swap\_annuity(swap\_times, df)}
\NormalTok{    option }\OperatorTok{=}\NormalTok{ gblack\_scholes(}
        \StringTok{"c"} \ControlFlowTok{if}\NormalTok{ is\_payer }\ControlFlowTok{else} \StringTok{"p"}\NormalTok{, swap\_rate, strike,}
\NormalTok{        swap\_times[}\DecValTok{0}\NormalTok{], }\FloatTok{0.0}\NormalTok{, }\FloatTok{0.0}\NormalTok{, vol}
\NormalTok{    )}

    \ControlFlowTok{return}\NormalTok{ notional }\OperatorTok{*}\NormalTok{ annuity }\OperatorTok{*}\NormalTok{ option}


\KeywordTok{def}\NormalTok{ swap\_value(notional, swap\_times, strike, df):}
    \CommentTok{\# positive notional means receiver swap like in Totoro}
\NormalTok{    swap\_rate, pv01 }\OperatorTok{=}\NormalTok{ swap\_annuity(swap\_times, df)}

    \ControlFlowTok{return}\NormalTok{ notional }\OperatorTok{*}\NormalTok{ pv01 }\OperatorTok{*}\NormalTok{ (strike }\OperatorTok{{-}}\NormalTok{ swap\_rate)}


\KeywordTok{def}\NormalTok{ swap\_annuity(swap\_times, df):}
\NormalTok{    dfs }\OperatorTok{=}\NormalTok{ np.array([df(T) }\ControlFlowTok{for}\NormalTok{ T }\KeywordTok{in}\NormalTok{ swap\_times])}
\NormalTok{    annuity }\OperatorTok{=}\NormalTok{ (dfs[}\DecValTok{1}\NormalTok{:] }\OperatorTok{*}\NormalTok{ (swap\_times[}\DecValTok{1}\NormalTok{:] }\OperatorTok{{-}}\NormalTok{ swap\_times[:}\OperatorTok{{-}}\DecValTok{1}\NormalTok{])).}\BuiltInTok{sum}\NormalTok{()}
\NormalTok{    swap\_rate }\OperatorTok{=}\NormalTok{ (dfs[}\DecValTok{0}\NormalTok{] }\OperatorTok{{-}}\NormalTok{ dfs[}\OperatorTok{{-}}\DecValTok{1}\NormalTok{]) }\OperatorTok{/}\NormalTok{ annuity}

    \ControlFlowTok{return}\NormalTok{ swap\_rate, annuity}


\CommentTok{\# Bootstrapping}

\KeywordTok{def}\NormalTok{ bootstrap\_zero\_rates(swap\_tenors, swap\_rates, period}\OperatorTok{=}\FloatTok{0.25}\NormalTok{):}
    \CommentTok{"""}
\CommentTok{    Bootstrap zero coupon rates from swap rates}
\CommentTok{    Assumes constant period between swap\_times}
\CommentTok{    and par swap rates as input}
\CommentTok{    """}
\NormalTok{    swap\_rates\_function }\OperatorTok{=}\NormalTok{ flat\_quadratic\_interpolator(swap\_tenors,}
\NormalTok{                                                      swap\_rates)}
\NormalTok{    zero\_rate\_tenors }\OperatorTok{=}\NormalTok{ np.arange(period,}
\NormalTok{                                 swap\_tenors[}\OperatorTok{{-}}\DecValTok{1}\NormalTok{] }\OperatorTok{+}\NormalTok{ period,}
\NormalTok{                                 period)}
\NormalTok{    interpolated\_swap\_rates }\OperatorTok{=}\NormalTok{ swap\_rates\_function(zero\_rate\_tenors)}

    \CommentTok{\# First zero rate equals first swap rate}
\NormalTok{    zero\_rates }\OperatorTok{=}\NormalTok{ []}
\NormalTok{    zero\_rates.append(interpolated\_swap\_rates[}\DecValTok{0}\NormalTok{])}
\NormalTok{    annuity }\OperatorTok{=}\NormalTok{ period }\OperatorTok{*}\NormalTok{ np.exp(}\OperatorTok{{-}}\NormalTok{zero\_rates[}\DecValTok{0}\NormalTok{] }\OperatorTok{*}\NormalTok{ zero\_rate\_tenors[}\DecValTok{0}\NormalTok{])}

    \CommentTok{\# Bootstrap remaining rates}
    \CommentTok{\# Solve for last\_df:}
    \CommentTok{\# swap\_rates[i] = (1 {-} last\_df)}
    \CommentTok{\#                 / (previous\_annuity + period * last\_df),}
    \CommentTok{\# then convert to zero rate}
    \ControlFlowTok{for}\NormalTok{ i }\KeywordTok{in} \BuiltInTok{range}\NormalTok{(}\DecValTok{1}\NormalTok{, }\BuiltInTok{len}\NormalTok{(interpolated\_swap\_rates)):}
\NormalTok{        last\_df }\OperatorTok{=}\NormalTok{ (}\DecValTok{1} \OperatorTok{{-}}\NormalTok{ interpolated\_swap\_rates[i] }\OperatorTok{*}\NormalTok{ annuity) }\OperatorTok{/}\NormalTok{ (}
            \DecValTok{1} \OperatorTok{+}\NormalTok{ interpolated\_swap\_rates[i] }\OperatorTok{*}\NormalTok{ period}
\NormalTok{        )}
\NormalTok{        new\_zero\_rates }\OperatorTok{=} \OperatorTok{{-}}\NormalTok{np.log(last\_df) }\OperatorTok{/}\NormalTok{ zero\_rate\_tenors[i]}

\NormalTok{        zero\_rates.append(new\_zero\_rates)}
\NormalTok{        annuity }\OperatorTok{+=}\NormalTok{ period }\OperatorTok{*}\NormalTok{ np.exp(}\OperatorTok{{-}}\NormalTok{new\_zero\_rates}
                                   \OperatorTok{*}\NormalTok{ zero\_rate\_tenors[i])}

    \ControlFlowTok{return}\NormalTok{ zero\_rate\_tenors, np.array(zero\_rates)}


\KeywordTok{def}\NormalTok{ new\_df(tenors, rates):  }\CommentTok{\# discount function}
\NormalTok{    rates\_function }\OperatorTok{=}\NormalTok{ flat\_quadratic\_interpolator(tenors, rates)}

    \ControlFlowTok{return} \KeywordTok{lambda}\NormalTok{ t: np.exp(}\OperatorTok{{-}}\NormalTok{t }\OperatorTok{*}\NormalTok{ rates\_function([t])[}\DecValTok{0}\NormalTok{])}

\CommentTok{\# Quadratic Interpolation }

\KeywordTok{def}\NormalTok{ flat\_quadratic\_interpolator(xs, ys):}
    \ControlFlowTok{return}\NormalTok{ interp1d(}
\NormalTok{        xs, ys,}
\NormalTok{        kind}\OperatorTok{=}\StringTok{"quadratic"}\NormalTok{,}
\NormalTok{        fill\_value}\OperatorTok{=}\NormalTok{(ys[}\DecValTok{0}\NormalTok{], ys[}\OperatorTok{{-}}\DecValTok{1}\NormalTok{]),}
\NormalTok{        bounds\_error}\OperatorTok{=}\VariableTok{False}
\NormalTok{    )}


\CommentTok{\# Zero{-}coupon Inflation Swap}

\KeywordTok{def}\NormalTok{ diff\_months(d1, d2):}
    \CommentTok{"""}
\CommentTok{    Computes the number of months between dates d1 and d2,}
\CommentTok{    assuming d1 \textless{}= d2.}
\CommentTok{    """}
    \ControlFlowTok{if}\NormalTok{ d2 }\OperatorTok{\textless{}=}\NormalTok{ d1:}
        \ControlFlowTok{return} \DecValTok{0}

    \ControlFlowTok{return}\NormalTok{ (d2.year }\OperatorTok{{-}}\NormalTok{ d1.year) }\OperatorTok{*} \DecValTok{12} \OperatorTok{+}\NormalTok{ d2.month }\OperatorTok{{-}}\NormalTok{ d1.month}


\KeywordTok{def}\NormalTok{ zero\_coupon\_inflation\_swap\_pricer(}
\NormalTok{    t0\_date,}
\NormalTok{    start\_date,}
\NormalTok{    maturity\_date,}
\NormalTok{    notional,}
\NormalTok{    fixed\_rate,}
\NormalTok{    cpi\_t0,}
\NormalTok{    cpi\_forward,}
\NormalTok{    cpi\_forward\_next\_month}\OperatorTok{=}\VariableTok{None}\NormalTok{,}
\NormalTok{    df}\OperatorTok{=}\VariableTok{None}\NormalTok{,}
\NormalTok{    linear\_indexation}\OperatorTok{=}\VariableTok{False}\NormalTok{,}
\NormalTok{):}
    \CommentTok{\# Handle CPI interpolation if enabled}
    \ControlFlowTok{if}\NormalTok{ linear\_indexation:}
        \ControlFlowTok{if}\NormalTok{ cpi\_forward\_next\_month }\KeywordTok{is} \VariableTok{None}\NormalTok{:}
            \ControlFlowTok{raise} \PreprocessorTok{ValueError}\NormalTok{(}
                \StringTok{"cpi\_forward\_next\_month is required "}
                \StringTok{"when linear\_indexation is True."}
\NormalTok{            )}

\NormalTok{        days\_in\_month }\OperatorTok{=}\NormalTok{ calendar.monthrange(maturity\_date.year,}
\NormalTok{                                            maturity\_date.month)[}\DecValTok{1}\NormalTok{]}
\NormalTok{        day }\OperatorTok{=}\NormalTok{ maturity\_date.day}
\NormalTok{        interpolated\_cpi }\OperatorTok{=}\NormalTok{ (}
\NormalTok{            cpi\_forward }\OperatorTok{+}\NormalTok{ (cpi\_forward\_next\_month }\OperatorTok{{-}}\NormalTok{ cpi\_forward)}
            \OperatorTok{*}\NormalTok{ day }\OperatorTok{/}\NormalTok{ days\_in\_month}
\NormalTok{        )}
    \ControlFlowTok{else}\NormalTok{:}
\NormalTok{        interpolated\_cpi }\OperatorTok{=}\NormalTok{ cpi\_forward}

    \CommentTok{\# Inflation leg}
\NormalTok{    inflation\_ratio }\OperatorTok{=}\NormalTok{ interpolated\_cpi }\OperatorTok{/}\NormalTok{ cpi\_t0}
\NormalTok{    inflation\_leg }\OperatorTok{=}\NormalTok{ notional }\OperatorTok{*}\NormalTok{ (inflation\_ratio }\OperatorTok{{-}} \DecValTok{1}\NormalTok{)}

    \CommentTok{\# Fixed leg}
\NormalTok{    months }\OperatorTok{=}\NormalTok{ diff\_months(start\_date, maturity\_date)}
\NormalTok{    fixed\_leg }\OperatorTok{=}\NormalTok{ notional }\OperatorTok{*}\NormalTok{ (}\BuiltInTok{pow}\NormalTok{(}\DecValTok{1} \OperatorTok{+}\NormalTok{ fixed\_rate, months }\OperatorTok{/} \DecValTok{12}\NormalTok{) }\OperatorTok{{-}} \DecValTok{1}\NormalTok{)}

    \CommentTok{\# Discount factor, assuming Act/360 convention}
\NormalTok{    dcf }\OperatorTok{=}\NormalTok{ (maturity\_date }\OperatorTok{{-}}\NormalTok{ t0\_date).days }\OperatorTok{/} \FloatTok{360.0}
\NormalTok{    discount }\OperatorTok{=}\NormalTok{ df(dcf) }\ControlFlowTok{if}\NormalTok{ df }\KeywordTok{is} \KeywordTok{not} \VariableTok{None} \ControlFlowTok{else} \FloatTok{1.0}

    \CommentTok{\# Present values}
\NormalTok{    pv\_inflation\_leg }\OperatorTok{=}\NormalTok{ inflation\_leg }\OperatorTok{*}\NormalTok{ discount}
\NormalTok{    pv\_fixed\_leg }\OperatorTok{=}\NormalTok{ fixed\_leg }\OperatorTok{*}\NormalTok{ discount}

    \CommentTok{\# Net present value}
\NormalTok{    npv }\OperatorTok{=}\NormalTok{ pv\_inflation\_leg }\OperatorTok{{-}}\NormalTok{ pv\_fixed\_leg}

    \ControlFlowTok{return}\NormalTok{ npv}
\end{Highlighting}
\end{Shaded}

\subsubsection{vanilla.py}\label{vanilla.py}

\begin{Shaded}
\begin{Highlighting}[numbers=left,,]
\ImportTok{import}\NormalTok{ math}
\ImportTok{from}\NormalTok{ scipy.stats }\ImportTok{import}\NormalTok{ norm}



\CommentTok{\# Generalized Black{-}Scholes pricing function}
\KeywordTok{def}\NormalTok{ gblack\_scholes(call\_put\_flag, S, x, T, r, b, v):}
\NormalTok{    d1 }\OperatorTok{=}\NormalTok{ ((math.log(S }\OperatorTok{/}\NormalTok{ x) }\OperatorTok{+}\NormalTok{ (b }\OperatorTok{+} \FloatTok{0.5} \OperatorTok{*}\NormalTok{ v}\OperatorTok{**}\DecValTok{2}\NormalTok{) }\OperatorTok{*}\NormalTok{ T)}
          \OperatorTok{/}\NormalTok{ (v }\OperatorTok{*}\NormalTok{ math.sqrt(T)))}
\NormalTok{    d2 }\OperatorTok{=}\NormalTok{ d1 }\OperatorTok{{-}}\NormalTok{ v }\OperatorTok{*}\NormalTok{ math.sqrt(T)}

    \ControlFlowTok{if}\NormalTok{ call\_put\_flag.lower() }\OperatorTok{==} \StringTok{"c"}\NormalTok{:}
        \ControlFlowTok{return}\NormalTok{ (S }\OperatorTok{*}\NormalTok{ math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(d1)}
                \OperatorTok{{-}}\NormalTok{ x }\OperatorTok{*}\NormalTok{ math.exp(}
            \OperatorTok{{-}}\NormalTok{r }\OperatorTok{*}\NormalTok{ T}
\NormalTok{        ) }\OperatorTok{*}\NormalTok{ norm.cdf(d2))}
    \ControlFlowTok{elif}\NormalTok{ call\_put\_flag.lower() }\OperatorTok{==} \StringTok{"p"}\NormalTok{:}
        \ControlFlowTok{return}\NormalTok{ (x }\OperatorTok{*}\NormalTok{ math.exp(}\OperatorTok{{-}}\NormalTok{r }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(}\OperatorTok{{-}}\NormalTok{d2)}
                \OperatorTok{{-}}\NormalTok{ S }\OperatorTok{*}\NormalTok{ math.exp(}
\NormalTok{            (b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T}
\NormalTok{        ) }\OperatorTok{*}\NormalTok{ norm.cdf(}\OperatorTok{{-}}\NormalTok{d1))}
    \ControlFlowTok{else}\NormalTok{:}
        \ControlFlowTok{raise} \PreprocessorTok{ValueError}\NormalTok{(}\StringTok{"call\_put\_flag must be \textquotesingle{}c\textquotesingle{} or \textquotesingle{}p\textquotesingle{}"}\NormalTok{)}


\CommentTok{\# Delta}
\KeywordTok{def}\NormalTok{ gdelta(call\_put\_flag, S, x, T, r, b, v):}
\NormalTok{    d1 }\OperatorTok{=}\NormalTok{ ((math.log(S }\OperatorTok{/}\NormalTok{ x) }\OperatorTok{+}\NormalTok{ (b }\OperatorTok{+} \FloatTok{0.5} \OperatorTok{*}\NormalTok{ v}\OperatorTok{**}\DecValTok{2}\NormalTok{) }\OperatorTok{*}\NormalTok{ T)}
          \OperatorTok{/}\NormalTok{ (v }\OperatorTok{*}\NormalTok{ math.sqrt(T)))}

    \ControlFlowTok{if}\NormalTok{ call\_put\_flag.lower() }\OperatorTok{==} \StringTok{"c"}\NormalTok{:}
        \ControlFlowTok{return}\NormalTok{ math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(d1)}
    \ControlFlowTok{else}\NormalTok{:}
        \ControlFlowTok{return} \OperatorTok{{-}}\NormalTok{math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(}\OperatorTok{{-}}\NormalTok{d1)}


\CommentTok{\# Gamma from Delta}
\KeywordTok{def}\NormalTok{ ggamma(S, T, r, b, v, delta):}
\NormalTok{    d1 }\OperatorTok{=}\NormalTok{ norm.ppf(math.exp((r }\OperatorTok{{-}}\NormalTok{ b) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*} \BuiltInTok{abs}\NormalTok{(delta))}

    \ControlFlowTok{return}\NormalTok{ (math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.pdf(d1)}
            \OperatorTok{/}\NormalTok{ (S }\OperatorTok{*}\NormalTok{ v }\OperatorTok{*}\NormalTok{ math.sqrt(T)))}


\CommentTok{\# Vega}
\KeywordTok{def}\NormalTok{ gvega(S, x, T, r, b, v):}
\NormalTok{    d1 }\OperatorTok{=}\NormalTok{ ((math.log(S }\OperatorTok{/}\NormalTok{ x) }\OperatorTok{+}\NormalTok{ (b }\OperatorTok{+} \FloatTok{0.5} \OperatorTok{*}\NormalTok{ v}\OperatorTok{**}\DecValTok{2}\NormalTok{) }\OperatorTok{*}\NormalTok{ T)}
          \OperatorTok{/}\NormalTok{ (v }\OperatorTok{*}\NormalTok{ math.sqrt(T)))}

    \ControlFlowTok{return}\NormalTok{ S }\OperatorTok{*}\NormalTok{ math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.pdf(d1) }\OperatorTok{*}\NormalTok{ math.sqrt(T)}


\CommentTok{\# Theta}
\KeywordTok{def}\NormalTok{ gtheta(call\_put\_flag, S, x, T, r, b, v):}
\NormalTok{    d1 }\OperatorTok{=}\NormalTok{ ((math.log(S }\OperatorTok{/}\NormalTok{ x) }\OperatorTok{+}\NormalTok{ (b }\OperatorTok{+} \FloatTok{0.5} \OperatorTok{*}\NormalTok{ v}\OperatorTok{**}\DecValTok{2}\NormalTok{) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{/}
\NormalTok{          (v }\OperatorTok{*}\NormalTok{ math.sqrt(T)))}
\NormalTok{    d2 }\OperatorTok{=}\NormalTok{ d1 }\OperatorTok{{-}}\NormalTok{ v }\OperatorTok{*}\NormalTok{ math.sqrt(T)}

    \ControlFlowTok{if}\NormalTok{ call\_put\_flag.lower() }\OperatorTok{==} \StringTok{"c"}\NormalTok{:}
        \ControlFlowTok{return}\NormalTok{ (}
            \OperatorTok{{-}}\NormalTok{S }\OperatorTok{*}\NormalTok{ math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.pdf(d1) }\OperatorTok{*}\NormalTok{ v}
            \OperatorTok{/}\NormalTok{ (}\DecValTok{2} \OperatorTok{*}\NormalTok{ math.sqrt(T))}
            \OperatorTok{{-}}\NormalTok{ (b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ S }\OperatorTok{*}\NormalTok{ math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(d1)}
            \OperatorTok{{-}}\NormalTok{ r }\OperatorTok{*}\NormalTok{ x }\OperatorTok{*}\NormalTok{ math.exp(}\OperatorTok{{-}}\NormalTok{r }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(d2)}
\NormalTok{        )}
    \ControlFlowTok{elif}\NormalTok{ call\_put\_flag.lower() }\OperatorTok{==} \StringTok{"p"}\NormalTok{:}
        \ControlFlowTok{return}\NormalTok{ (}
            \OperatorTok{{-}}\NormalTok{S }\OperatorTok{*}\NormalTok{ math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.pdf(d1) }\OperatorTok{*}\NormalTok{ v}
            \OperatorTok{/}\NormalTok{ (}\DecValTok{2} \OperatorTok{*}\NormalTok{ math.sqrt(T))}
            \OperatorTok{+}\NormalTok{ (b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ S }\OperatorTok{*}\NormalTok{ math.exp((b }\OperatorTok{{-}}\NormalTok{ r) }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(}\OperatorTok{{-}}\NormalTok{d1)}
            \OperatorTok{+}\NormalTok{ r }\OperatorTok{*}\NormalTok{ x }\OperatorTok{*}\NormalTok{ math.exp(}\OperatorTok{{-}}\NormalTok{r }\OperatorTok{*}\NormalTok{ T) }\OperatorTok{*}\NormalTok{ norm.cdf(}\OperatorTok{{-}}\NormalTok{d2)}
\NormalTok{        )}
    \ControlFlowTok{else}\NormalTok{:}
        \ControlFlowTok{raise} \PreprocessorTok{ValueError}\NormalTok{(}\StringTok{"call\_put\_flag must be \textquotesingle{}c\textquotesingle{} or \textquotesingle{}p\textquotesingle{}"}\NormalTok{)}


\CommentTok{\# Newton\textquotesingle{}s Method implementation}

\KeywordTok{def}\NormalTok{ implied\_volatility(}
\NormalTok{    market\_price, S, x, T, r, b,}
\NormalTok{    call\_put\_flag,}
\NormalTok{    initial\_vol\_guess}\OperatorTok{=}\FloatTok{0.25}\NormalTok{,}
\NormalTok{    tol}\OperatorTok{=}\FloatTok{1e{-}7}\NormalTok{,}
\NormalTok{    max\_iter}\OperatorTok{=}\DecValTok{200}\NormalTok{,}
\NormalTok{):}
    \KeywordTok{def}\NormalTok{ func\_to\_solve(v\_candidate):}
        \ControlFlowTok{return}\NormalTok{ (gblack\_scholes(call\_put\_flag, S, x, T, r,}
\NormalTok{                               b, v\_candidate)}
                \OperatorTok{{-}}\NormalTok{ market\_price)}

    \KeywordTok{def}\NormalTok{ func\_to\_solve\_prime(v\_candidate):}
        \ControlFlowTok{return}\NormalTok{ gvega(S, x, T, r, b, v\_candidate)}

\NormalTok{    implied\_v }\OperatorTok{=}\NormalTok{ newton\_raphson\_solver(}
\NormalTok{        f}\OperatorTok{=}\NormalTok{func\_to\_solve,}
\NormalTok{        f\_prime}\OperatorTok{=}\NormalTok{func\_to\_solve\_prime,}
\NormalTok{        initial\_guess}\OperatorTok{=}\NormalTok{initial\_vol\_guess,}
\NormalTok{        tol}\OperatorTok{=}\NormalTok{tol,}
\NormalTok{        max\_iter}\OperatorTok{=}\NormalTok{max\_iter,}
\NormalTok{    )}

    \ControlFlowTok{return}\NormalTok{ implied\_v}


\KeywordTok{def}\NormalTok{ newton\_raphson\_solver(f, f\_prime, initial\_guess, tol}\OperatorTok{=}\FloatTok{1e{-}7}\NormalTok{,}
\NormalTok{                          max\_iter}\OperatorTok{=}\DecValTok{200}\NormalTok{):}
\NormalTok{    x }\OperatorTok{=}\NormalTok{ initial\_guess}

    \ControlFlowTok{for}\NormalTok{ i }\KeywordTok{in} \BuiltInTok{range}\NormalTok{(max\_iter):}
\NormalTok{        f\_val }\OperatorTok{=}\NormalTok{ f(x)}
\NormalTok{        f\_prime\_val }\OperatorTok{=}\NormalTok{ f\_prime(x)}

        \ControlFlowTok{if} \BuiltInTok{abs}\NormalTok{(f\_val) }\OperatorTok{\textless{}}\NormalTok{ tol:}
            \ControlFlowTok{return}\NormalTok{ x}
        \ControlFlowTok{if} \BuiltInTok{abs}\NormalTok{(f\_prime\_val) }\OperatorTok{\textless{}} \FloatTok{1e{-}12}\NormalTok{:}
            \BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Warning: Derivative too close to zero "}
                  \SpecialStringTok{f"at x = }\SpecialCharTok{\{}\NormalTok{x}\SpecialCharTok{\}}\SpecialStringTok{. Cannot proceed."}\NormalTok{)}
            \ControlFlowTok{return} \VariableTok{None}

\NormalTok{        x }\OperatorTok{{-}=}\NormalTok{ f\_val }\OperatorTok{/}\NormalTok{ f\_prime\_val}

        \ControlFlowTok{if}\NormalTok{ x }\OperatorTok{\textless{}} \DecValTok{0}\NormalTok{:}
\NormalTok{            x }\OperatorTok{=} \FloatTok{0.001}

    \BuiltInTok{print}\NormalTok{(}\SpecialStringTok{f"Warning: Newton{-}Raphson did not converge "}
          \SpecialStringTok{f"after }\SpecialCharTok{\{}\NormalTok{max\_iter}\SpecialCharTok{\}}\SpecialStringTok{ iterations."}\NormalTok{)}

    \ControlFlowTok{return} \VariableTok{None}
\end{Highlighting}
\end{Shaded}




% Appendix - Include external PDF
\newpage
\includepdf[pages=-]{signature.pdf}


\end{document}
