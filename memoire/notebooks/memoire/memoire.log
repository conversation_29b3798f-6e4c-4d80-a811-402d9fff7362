This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.6.1)  6 JUL 2025 22:18
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**memoire.tex
(./memoire.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-04-29>
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count268
\c@section=\count269
\c@subsection=\count270
\c@subsubsection=\count271
\c@paragraph=\count272
\c@subparagraph=\count273
\c@figure=\count274
\c@table=\count275
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen146
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/xcolor/svgnam.def
File: svgnam.def 2024/09/29 v3.02 Predefined colors according to SVG 1.1 (UK)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/xcolor/x11nam.def
File: x11nam.def 2024/09/29 v3.02 Predefined colors according to Unix/X11 (UK)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen147
)) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen148
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count276
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count277
\leftroot@=\count278
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count279
\DOTSCASE@=\count280
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen149
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count281
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count282
\dotsspace@=\muskip17
\c@parentequation=\count283
\dspbrk@lvl=\count284
\tag@help=\toks18
\row@=\count285
\column@=\count286
\maxfields@=\count287
\andhelp@=\toks19
\eqnshift@=\dimen150
\alignsep@=\dimen151
\tagshift@=\dimen152
\tagwidth@=\dimen153
\totwidth@=\dimen154
\lineht@=\dimen155
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math.sty (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-04-29 L3 programming layer (loader) 
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2025-04-14 L3 backend support: XeTeX
\g__graphics_track_int=\count288
\g__pdfannot_backend_int=\count289
\g__pdfannot_backend_link_int=\count290
))
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.sty
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count291
\l__fontspec_language_int=\count292
\l__fontspec_strnum_int=\count293
\l__fontspec_tmp_int=\count294
\l__fontspec_tmpa_int=\count295
\l__fontspec_tmpb_int=\count296
\l__fontspec_tmpc_int=\count297
\l__fontspec_em_int=\count298
\l__fontspec_emdef_int=\count299
\l__fontspec_strong_int=\count300
\l__fontspec_strongdef_int=\count301
\l__fontspec_tmpa_dim=\dimen156
\l__fontspec_tmpb_dim=\dimen157
\l__fontspec_tmpc_dim=\dimen158
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\g__um_fam_int=\count302
\g__um_fonts_used_int=\count303
\l__um_primecount_int=\count304
\g__um_primekern_muskip=\muskip18
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math-table.tex))) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/lm/lmodern.sty
Package: lmodern 2015/05/01 v1.6.1 Latin Modern Fonts
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/lmr/m/n on input line 22.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/lmm/m/it on input line 23.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/lmsy/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 26.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/lmm/b/it on input line 27.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/lmsy/b/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 29.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/lmss/m/n on input line 32.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/lmr/m/it on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/lmss/bx/n on input line 36.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/lmr/bx/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 38.
)

Package fontspec Info: 
(fontspec)             Times New Roman scale = 0.963629354430423.


Package fontspec Info: 
(fontspec)             Times New Roman scale = 1.


Package fontspec Info: 
(fontspec)             Times New Roman scale = 0.963629354430423.


Package fontspec Info: 
(fontspec)             Times New Roman scale = 1.


Package fontspec Info: 
(fontspec)             Times New Roman/B scale = 0.944047646899576.


Package fontspec Info: 
(fontspec)             Times New Roman/B scale = 1.


Package fontspec Info: 
(fontspec)             Times New Roman/I scale = 1.001911883116192.


Package fontspec Info: 
(fontspec)             Times New Roman/I scale = 1.


Package fontspec Info: 
(fontspec)             Times New Roman/BI scale = 0.9818514257111176.


Package fontspec Info: 
(fontspec)             Times New Roman/BI scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'TimesNewRoman(0)' created for font 'Times
(fontspec)             New Roman' with options
(fontspec)             [Scale=MatchLowercase,Ligatures=TeX,Scale=1].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->s*[1]"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->s*[1]"Times New
(fontspec)             Roman/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->s*[1]"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->s*[1]"Times
(fontspec)             New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;mapping=tex-text;"

LaTeX Font Info:    Font shape `TU/TimesNewRoman(0)/m/n' will be
(Font)              scaled to size 10.0pt on input line 26.

Package fontspec Info: 
(fontspec)             Times New Roman scale = 0.9999965884395865.


Package fontspec Info: 
(fontspec)             Times New Roman scale = 0.9999965884395865.


Package fontspec Info: 
(fontspec)             Times New Roman/B scale = 0.9796758700672793.


Package fontspec Info: 
(fontspec)             Times New Roman/I scale = 1.039723894282441.


Package fontspec Info: 
(fontspec)             Times New Roman/BI scale = 1.018906358084128.


Package fontspec Info: 
(fontspec)             Font family 'TimesNewRoman(1)' created for font 'Times
(fontspec)             New Roman' with options [Scale=MatchLowercase].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[0.9999965884395865]"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9796758700672793]"Times New
(fontspec)             Roman/B/OT:script=latn;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1.039723894282441]"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1.018906358084128]"Times New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;"

LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/lmr/m/n --> TU/TimesNewRoman(1)/m/n on input line 26.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/lmr/m/it --> TU/TimesNewRoman(1)/m/it on input line 26.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/lmr/bx/n --> TU/TimesNewRoman(1)/b/n on input line 26.
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/upquote/upquote.sty
Package: upquote 2012/04/19 v1.3 upright-quote and grave-accent glyphs in verbatim
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count305
)
\MT@toks=\toks23
\MT@tempbox=\box55
\MT@count=\count306
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks24
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen159
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count307
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/microtype/microtype-xetex.def
File: microtype-xetex.def 2025/02/11 v3.2a Definitions specific to xetex (RS)
LaTeX Info: Redefining \lsstyle on input line 443.
LaTeX Info: Redefining \lslig on input line 451.
\MT@outer@space=\skip54
)
Package microtype Info: Loading configuration file microtype.cfg.
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/fancyvrb/fancyvrb.sty
Package: fancyvrb 2024/01/20 4.5c verbatim text (tvz,hv)
\FV@CodeLineNo=\count308
\FV@InFile=\read2
\FV@TabBox=\box56
\c@FancyVerbLine=\count309
\FV@StepNumber=\count310
\FV@OutFile=\write3
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/framed/framed.sty
Package: framed 2011/10/22 v 0.96: framed or shaded text with page breaks
\OuterFrameSep=\skip55
\fb@frw=\dimen160
\fb@frh=\dimen161
\FrameRule=\dimen162
\FrameSep=\dimen163
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2024-10-27 v4.22 Multi-page Table package (DPC)
\LTleft=\skip56
\LTright=\skip57
\LTpre=\skip58
\LTpost=\skip59
\LTchunksize=\count311
\LTcapwidth=\dimen164
\LT@head=\box57
\LT@firsthead=\box58
\LT@foot=\box59
\LT@lastfoot=\box60
\LT@gbox=\box61
\LT@cols=\count312
\LT@rows=\count313
\c@LT@tables=\count314
\c@LT@chunks=\count315
\LT@p@ftn=\toks25
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen165
\lightrulewidth=\dimen166
\cmidrulewidth=\dimen167
\belowrulesep=\dimen168
\belowbottomsep=\dimen169
\aboverulesep=\dimen170
\abovetopsep=\dimen171
\cmidrulesep=\dimen172
\cmidrulekern=\dimen173
\defaultaddspace=\dimen174
\@cmidla=\count316
\@cmidlb=\count317
\@aboverulesep=\dimen175
\@belowrulesep=\dimen176
\@thisruleclass=\count318
\@lastruleclass=\count319
\@thisrulewidth=\dimen177
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen178
\ar@mcellbox=\box62
\extrarowheight=\dimen179
\NC@list=\toks26
\extratabsurround=\skip60
\backup@length=\skip61
\ar@cellbox=\box63
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count320
\calc@Bcount=\count321
\calc@Adimen=\dimen180
\calc@Bdimen=\dimen181
\calc@Askip=\skip62
\calc@Bskip=\skip63
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count322
\calc@Cskip=\skip64
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/mdwtools/footnote.sty
Package: footnote 1997/01/28 1.13 Save footnotes around boxes
\fn@notes=\box64
\fn@width=\dimen182
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
)
\Gin@req@height=\dimen183
\Gin@req@width=\dimen184
)
\pandoc@box=\box65
\cslhangindent=\skip65
\csllabelwidth=\skip66
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/pdfpages/pdfpages.sty
Package: pdfpages 2025/03/12 v0.6f Insert pages of external PDF documents (AM)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/eso-pic/eso-pic.sty
Package: eso-pic 2023/05/03 v3.0c eso-pic (RN)
\ESO@tempdima=\dimen185
\ESO@tempdimb=\dimen186
)
\AM@pagewidth=\dimen187
\AM@pageheight=\dimen188
\AM@fboxrule=\dimen189
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/pdfpages/ppxetex.def
File: ppxetex.def 2025/03/12 v0.6f Pdfpages driver for XeTeX (AM)
)
\pdfpages@includegraphics@status=\count323
\AM@pagebox=\box66
\AM@global@opts=\toks27
\AM@pagecnt=\count324
\AM@toc@title=\toks28
\AM@lof@heading=\toks29
\c@AM@survey=\count325
\AM@templatesizebox=\box67
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen190
\captionmargin=\dimen191
\caption@leftmargin=\dimen192
\caption@rightmargin=\dimen193
\caption@width=\dimen194
\caption@indent=\dimen195
\caption@parindent=\dimen196
\caption@hangindent=\dimen197
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count326
\c@continuedfloat=\count327
Package caption Info: longtable package is loaded.
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/caption/ltcaption.sty
Package: ltcaption 2021/01/08 v1.4c longtable captions (AR)
)) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count328
\float@exts=\toks30
\float@box=\box68
\@float@everytoks=\toks31
\@floatcapt=\box69
)
\@float@every@codelisting=\toks32
\c@codelisting=\count329
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count330
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count331
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/bookmark/bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2025-05-20 v7.01m Hypertext links for LaTeX
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
(/Users/<USER>/Library/TinyTeX/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/Users/<USER>/Library/TinyTeX/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count332
! Interruption.
\tl_if_head_is_N_type:nTF ...N {\token_to_str:N #1
                                                  }}\scan_stop: \scan_stop: ...
l.292 }}{}
          
You rang?
Try to insert an instruction for me (e.g., `I\showlists'),
unless you just want to quit by typing `X'.

 
Here is how much of TeX's memory you used:
 14888 strings out of 472186
 289287 string characters out of 5526802
 777138 words of memory out of 5000000
 41263 multiletter control sequences out of 15000+600000
 626929 words of font info for 53 fonts, out of 8000000 for 9000
 36 hyphenation exceptions out of 8191
 90i,0n,116p,512b,263s stack positions out of 10000i,1000n,20000p,200000b,200000s
No pages of output.
